'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeftIcon, 
  ClockIcon, 
  CalendarIcon, 
  UserIcon,
  ShareIcon,
  PaperAirplaneIcon,
  LinkIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';
import toast from 'react-hot-toast';

interface Meeting {
  id: string;
  title: string;
  platform: string;
  meeting_url?: string;
  duration_minutes: number;
  recorded_at: string;
  transcription_status: string;
  summary?: string;
  transcript?: string;
  client_summary?: string;
  project?: {
    id: string;
    name: string;
    client_name: string;
    client_email?: string;
  };
}

interface ActionItem {
  id: string;
  description: string;
  assigned_to?: string;
  due_date?: string;
  status: string;
  priority: string;
}

export default function MeetingDetailPage() {
  const params = useParams();
  const meetingId = params.id as string;
  
  const [meeting, setMeeting] = useState<Meeting | null>(null);
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [activeTab, setActiveTab] = useState('summary');
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingClientSummary, setIsGeneratingClientSummary] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [shareableLink, setShareableLink] = useState<string | null>(null);

  useEffect(() => {
    if (meetingId) {
      fetchMeetingData();
    }
  }, [meetingId]);

  const fetchMeetingData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch meeting details
      const meetingResponse = await apiHelpers.getMeeting(meetingId);
      if (meetingResponse.data.success) {
        setMeeting(meetingResponse.data.data);
      }

      // Fetch action items
      const actionItemsResponse = await apiHelpers.getMeetingActionItems(meetingId);
      if (actionItemsResponse.data.success) {
        setActionItems(actionItemsResponse.data.data);
      }
      
    } catch (error) {
      console.error('Error fetching meeting data:', error);
      toast.error('Failed to load meeting data');
    } finally {
      setIsLoading(false);
    }
  };

  const generateClientSummary = async () => {
    if (!meeting) return;
    
    try {
      setIsGeneratingClientSummary(true);
      const response = await apiHelpers.generateClientSummary(meetingId);
      
      if (response.data.success) {
        setMeeting(prev => prev ? { ...prev, client_summary: response.data.data.client_summary } : null);
        toast.success('Client summary generated successfully');
      }
    } catch (error) {
      console.error('Error generating client summary:', error);
      toast.error('Failed to generate client summary');
    } finally {
      setIsGeneratingClientSummary(false);
    }
  };

  const sendClientEmail = async () => {
    if (!meeting?.project?.client_email || !meeting.client_summary) return;
    
    try {
      setIsSendingEmail(true);
      const response = await apiHelpers.sendClientSummaryEmail(meetingId);
      
      if (response.data.success) {
        toast.success('Email sent to client successfully');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('Failed to send email to client');
    } finally {
      setIsSendingEmail(false);
    }
  };

  const generateShareableLink = async () => {
    try {
      const response = await apiHelpers.generateShareableLink(meetingId);
      
      if (response.data.success) {
        setShareableLink(response.data.data.link);
        navigator.clipboard.writeText(response.data.data.link);
        toast.success('Shareable link copied to clipboard');
      }
    } catch (error) {
      console.error('Error generating shareable link:', error);
      toast.error('Failed to generate shareable link');
    }
  };

  const tabs = [
    { id: 'summary', name: 'Summary', icon: DocumentTextIcon },
    { id: 'transcript', name: 'Transcript', icon: DocumentTextIcon },
    { id: 'action-items', name: 'Action Items', icon: DocumentTextIcon },
    { id: 'client-summary', name: 'Client Summary', icon: ShareIcon },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!meeting) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Meeting not found</h1>
            <Link href="/meetings" className="text-primary-600 hover:text-primary-500 mt-4 inline-block">
              ← Back to Meetings
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href={meeting.project ? `/projects/${meeting.project.id}` : "/meetings"}
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            {meeting.project ? `Back to ${meeting.project.name}` : 'Back to Meetings'}
          </Link>
          
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-start justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{meeting.title}</h1>
                <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                  <span className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    {new Date(meeting.recorded_at).toLocaleDateString()}
                  </span>
                  <span className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {meeting.duration_minutes} minutes
                  </span>
                  {meeting.project && (
                    <span className="flex items-center">
                      <UserIcon className="h-4 w-4 mr-1" />
                      {meeting.project.client_name}
                    </span>
                  )}
                </div>
              </div>
              <span className={`px-3 py-1 text-sm rounded-full ${
                meeting.transcription_status === 'completed' 
                  ? 'bg-green-100 text-green-800'
                  : meeting.transcription_status === 'processing'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {meeting.transcription_status}
              </span>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'summary' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">Meeting Summary</h2>
                {meeting.summary ? (
                  <div className="prose max-w-none">
                    <p className="text-gray-700 whitespace-pre-wrap">{meeting.summary}</p>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">
                      {meeting.transcription_status === 'completed' 
                        ? 'Summary is being generated...'
                        : 'Summary will be available after transcription is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'transcript' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">Full Transcript</h2>
                {meeting.transcript ? (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono">
                      {meeting.transcript}
                    </pre>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">
                      {meeting.transcription_status === 'processing' 
                        ? 'Transcript is being processed...'
                        : meeting.transcription_status === 'completed'
                        ? 'Transcript not available.'
                        : 'Transcript will be available after processing is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'action-items' && (
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">Action Items</h2>
                {actionItems.length > 0 ? (
                  <div className="space-y-4">
                    {actionItems.map((item) => (
                      <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="text-gray-900">{item.description}</p>
                            {item.assigned_to && (
                              <p className="text-sm text-gray-500 mt-1">
                                Assigned to: {item.assigned_to}
                              </p>
                            )}
                            {item.due_date && (
                              <p className="text-sm text-gray-500 mt-1">
                                Due: {new Date(item.due_date).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              item.priority === 'high'
                                ? 'bg-red-100 text-red-800'
                                : item.priority === 'medium'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {item.priority}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              item.status === 'completed'
                                ? 'bg-green-100 text-green-800'
                                : item.status === 'in_progress'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {item.status.replace('_', ' ')}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">
                      {meeting.transcription_status === 'completed'
                        ? 'No action items identified in this meeting.'
                        : 'Action items will be extracted after transcription is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'client-summary' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-medium text-gray-900">Client Summary</h2>
                  <div className="flex space-x-3">
                    {!meeting.client_summary && (
                      <button
                        onClick={generateClientSummary}
                        disabled={isGeneratingClientSummary || meeting.transcription_status !== 'completed'}
                        className="btn btn-secondary"
                      >
                        {isGeneratingClientSummary ? 'Generating...' : 'Generate Summary'}
                      </button>
                    )}
                    {meeting.client_summary && (
                      <>
                        <button
                          onClick={generateShareableLink}
                          className="btn btn-secondary"
                        >
                          <LinkIcon className="h-4 w-4 mr-2" />
                          Generate Link
                        </button>
                        {meeting.project?.client_email && (
                          <button
                            onClick={sendClientEmail}
                            disabled={isSendingEmail}
                            className="btn btn-primary"
                          >
                            <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                            {isSendingEmail ? 'Sending...' : 'Send Email'}
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </div>

                {meeting.client_summary ? (
                  <div className="space-y-6">
                    {/* Email Preview */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-sm font-medium text-gray-900 mb-4">Email Preview</h3>
                      <div className="bg-white border rounded-lg p-4">
                        <div className="border-b pb-3 mb-4">
                          <div className="text-sm text-gray-600">
                            <strong>To:</strong> {meeting.project?.client_email || '<EMAIL>'}
                          </div>
                          <div className="text-sm text-gray-600">
                            <strong>Subject:</strong> Meeting Summary - {meeting.title}
                          </div>
                        </div>
                        <div className="prose max-w-none">
                          <div className="whitespace-pre-wrap text-gray-700">
                            {meeting.client_summary}
                          </div>
                        </div>
                        <div className="mt-6 pt-4 border-t">
                          <p className="text-sm text-gray-600">
                            Want to access the full project details?
                            <button className="ml-2 text-primary-600 hover:text-primary-500 font-medium">
                              Join Project →
                            </button>
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Shareable Link */}
                    {shareableLink && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h3 className="text-sm font-medium text-blue-900 mb-2">Shareable Link</h3>
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={shareableLink}
                            readOnly
                            className="flex-1 text-sm bg-white border border-blue-200 rounded px-3 py-2"
                          />
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(shareableLink);
                              toast.success('Link copied to clipboard');
                            }}
                            className="btn btn-secondary text-sm"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-blue-600 mt-2">
                          This link allows clients to view the summary and request project access.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <ShareIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No client summary yet</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {meeting.transcription_status === 'completed'
                        ? 'Generate a client-friendly summary to share with your client.'
                        : 'Client summary will be available after transcription is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
          </div>
        </div>
      </div>
    </div>
  );
}
