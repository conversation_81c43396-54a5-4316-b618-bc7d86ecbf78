'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  ShareIcon,
  PaperAirplaneIcon,
  LinkIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';
import toast from 'react-hot-toast';

interface Meeting {
  id: string;
  title: string;
  platform: string;
  meeting_url?: string;
  duration_minutes: number;
  recorded_at: string;
  transcription_status: string;
  summary?: string;
  transcript?: string;
  client_summary?: string;
  project?: {
    id: string;
    name: string;
    client_name: string;
    client_email?: string;
  };
}

interface ActionItem {
  id: string;
  description: string;
  assigned_to?: string;
  due_date?: string;
  status: string;
  priority: string;
}

export default function MeetingDetailPage() {
  const params = useParams();
  const meetingId = params.id as string;
  
  const [meeting, setMeeting] = useState<Meeting | null>(null);
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [activeTab, setActiveTab] = useState('summary');
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingClientSummary, setIsGeneratingClientSummary] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [shareableLink, setShareableLink] = useState<string | null>(null);

  useEffect(() => {
    if (meetingId) {
      fetchMeetingData();
    }
  }, [meetingId]);

  const fetchMeetingData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch meeting details
      const meetingResponse = await apiHelpers.getMeeting(meetingId);
      if (meetingResponse.data.success) {
        setMeeting(meetingResponse.data.data);
      }

      // Fetch action items
      const actionItemsResponse = await apiHelpers.getMeetingActionItems(meetingId);
      if (actionItemsResponse.data.success) {
        setActionItems(actionItemsResponse.data.data);
      }
      
    } catch (error) {
      console.error('Error fetching meeting data:', error);
      toast.error('Failed to load meeting data');
    } finally {
      setIsLoading(false);
    }
  };

  const generateClientSummary = async () => {
    if (!meeting) return;
    
    try {
      setIsGeneratingClientSummary(true);
      const response = await apiHelpers.generateClientSummary(meetingId);
      
      if (response.data.success) {
        setMeeting(prev => prev ? { ...prev, client_summary: response.data.data.client_summary } : null);
        toast.success('Client summary generated successfully');
      }
    } catch (error) {
      console.error('Error generating client summary:', error);
      toast.error('Failed to generate client summary');
    } finally {
      setIsGeneratingClientSummary(false);
    }
  };

  const sendClientEmail = async () => {
    if (!meeting?.project?.client_email || !meeting.client_summary) return;
    
    try {
      setIsSendingEmail(true);
      const response = await apiHelpers.sendClientSummaryEmail(meetingId);
      
      if (response.data.success) {
        toast.success('Email sent to client successfully');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('Failed to send email to client');
    } finally {
      setIsSendingEmail(false);
    }
  };

  const generateShareableLink = async () => {
    try {
      const response = await apiHelpers.generateShareableLink(meetingId);
      
      if (response.data.success) {
        setShareableLink(response.data.data.link);
        navigator.clipboard.writeText(response.data.data.link);
        toast.success('Shareable link copied to clipboard');
      }
    } catch (error) {
      console.error('Error generating shareable link:', error);
      toast.error('Failed to generate shareable link');
    }
  };

  const tabs = [
    { id: 'summary', name: 'Summary', icon: DocumentTextIcon },
    { id: 'transcript', name: 'Transcript', icon: DocumentTextIcon },
    { id: 'action-items', name: 'Action Items', icon: DocumentTextIcon },
    { id: 'client-summary', name: 'Client Summary', icon: ShareIcon },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!meeting) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Meeting not found</h1>
            <Link href="/meetings" className="text-primary-600 hover:text-primary-500 mt-4 inline-block">
              ← Back to Meetings
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href={meeting.project ? `/projects/${meeting.project.id}` : "/meetings"}
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            {meeting.project ? `Back to ${meeting.project.name}` : 'Back to Meetings'}
          </Link>
          
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-6 border-b border-gray-200">
              <div className="flex items-start justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{meeting.title}</h1>
                  <p className="text-gray-600">{meeting.project?.client_name}</p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      const blob = new Blob([meeting.transcript || ''], { type: 'text/plain' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `${meeting.title}-transcript.txt`;
                      a.click();
                      URL.revokeObjectURL(url);
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 border border-gray-300 rounded-lg"
                    title="Download"
                  >
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </button>
                  <button
                    onClick={generateShareableLink}
                    className="p-2 text-gray-400 hover:text-gray-600 border border-gray-300 rounded-lg"
                    title="Share"
                  >
                    <ShareIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Meeting Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center text-gray-500 text-sm mb-1">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    Date & Time
                  </div>
                  <div className="font-semibold text-gray-900">
                    {new Date(meeting.recorded_at).toLocaleDateString()}
                  </div>
                  <div className="text-sm text-gray-600">
                    {new Date(meeting.recorded_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center text-gray-500 text-sm mb-1">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    Duration
                  </div>
                  <div className="font-semibold text-gray-900">{meeting.duration_minutes} minutes</div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center text-gray-500 text-sm mb-1">
                    <UserIcon className="h-4 w-4 mr-2" />
                    Attendees
                  </div>
                  <div className="font-semibold text-gray-900">3 people</div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center text-gray-500 text-sm mb-1">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                    Cost
                  </div>
                  <div className="font-semibold text-gray-900">$112.5</div>
                </div>
              </div>
            </div>
        </div>

            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

          <div className="p-6">
            {activeTab === 'summary' && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Meeting Overview</h2>
                {meeting.summary ? (
                  <div className="prose max-w-none">
                    <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                      {meeting.summary}
                    </div>

                    <div className="mt-8">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Key Decisions</h3>
                      <div className="text-gray-700">
                        <p>• Technology stack confirmed: React and Node.js for optimal performance and scalability</p>
                        <p>• Project timeline: 8-10 weeks with weekly check-ins every Friday</p>
                        <p>• Design approach: Mobile-first responsive design with focus on user experience</p>
                        <p>• Communication: Weekly progress meetings and Slack for daily updates</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      {meeting.transcription_status === 'completed'
                        ? 'Generating Summary...'
                        : 'Summary Pending'
                      }
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {meeting.transcription_status === 'completed'
                        ? 'AI is analyzing the meeting content to generate a comprehensive summary.'
                        : 'The summary will be automatically generated after transcription is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'transcript' && (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Full Transcript</h2>
                  {meeting.transcript && (
                    <button
                      onClick={() => {
                        const blob = new Blob([meeting.transcript || ''], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${meeting.title}-transcript.txt`;
                        a.click();
                        URL.revokeObjectURL(url);
                      }}
                      className="text-blue-600 hover:text-blue-500 font-medium text-sm"
                    >
                      Download
                    </button>
                  )}
                </div>
                {meeting.transcript ? (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="prose max-w-none">
                      <div className="whitespace-pre-wrap text-sm text-gray-700 leading-relaxed font-mono">
                        {meeting.transcript}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      {meeting.transcription_status === 'processing'
                        ? 'Processing Transcript...'
                        : meeting.transcription_status === 'completed'
                        ? 'Transcript Not Available'
                        : 'Transcript Pending'
                      }
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {meeting.transcription_status === 'processing'
                        ? 'Your transcript is being processed and will be available shortly.'
                        : meeting.transcription_status === 'completed'
                        ? 'The transcript could not be generated for this meeting.'
                        : 'The transcript will be available after processing is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'action-items' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">Your Action Items</h2>
                  {actionItems.length > 0 && (
                    <button className="text-blue-600 hover:text-blue-500 font-medium text-sm">
                      View All Tasks
                    </button>
                  )}
                </div>

                {actionItems.length > 0 ? (
                  <div className="space-y-4">
                    {actionItems.map((item) => (
                      <div key={item.id} className="flex items-start space-x-3 py-3">
                        <button
                          onClick={() => {
                            const newStatus = item.status === 'completed' ? 'pending' : 'completed';
                            setActionItems(actionItems.map(ai =>
                              ai.id === item.id ? {...ai, status: newStatus} : ai
                            ));
                            toast.success(`Action item marked as ${newStatus === 'completed' ? 'complete' : 'incomplete'}`);
                          }}
                          className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                            item.status === 'completed'
                              ? 'bg-green-500 border-green-500 text-white'
                              : 'border-gray-300 hover:border-green-400'
                          }`}
                        >
                          {item.status === 'completed' && (
                            <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </button>
                        <div className="flex-1 flex items-center justify-between">
                          <div>
                            <p className={`text-gray-900 ${item.status === 'completed' ? 'line-through text-gray-500' : ''}`}>
                              {item.description}
                            </p>
                            {item.due_date && (
                              <p className="text-sm text-gray-500 mt-1">
                                Due: {new Date(item.due_date).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              item.priority === 'high'
                                ? 'bg-red-100 text-red-700'
                                : item.priority === 'medium'
                                ? 'bg-yellow-100 text-yellow-700'
                                : 'bg-green-100 text-green-700'
                            }`}>
                              {item.priority}
                            </span>
                            {item.priority === 'high' && item.status !== 'completed' && (
                              <span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-full">
                                urgent
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      {meeting.transcription_status === 'completed'
                        ? 'No Action Items Found'
                        : 'Action Items Pending'
                      }
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {meeting.transcription_status === 'completed'
                        ? 'No action items were identified in this meeting.'
                        : 'Action items will be automatically extracted after transcription is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'client-summary' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">Professional Summary</h2>
                  <div className="flex space-x-3">
                    {!meeting.client_summary && (
                      <button
                        onClick={generateClientSummary}
                        disabled={isGeneratingClientSummary || meeting.transcription_status !== 'completed'}
                        className="btn btn-secondary"
                      >
                        {isGeneratingClientSummary ? 'Generating...' : 'Generate Summary'}
                      </button>
                    )}
                    {meeting.client_summary && (
                      <>
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(meeting.client_summary || '');
                            toast.success('Summary copied to clipboard');
                          }}
                          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center"
                        >
                          <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          Copy
                        </button>
                        <button
                          onClick={sendClientEmail}
                          disabled={isSendingEmail}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                        >
                          <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                          {isSendingEmail ? 'Sending...' : 'Send Email'}
                        </button>
                      </>
                    )}
                  </div>
                </div>

                {meeting.client_summary ? (
                  <div className="space-y-6">
                    {/* Professional Summary Content */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="prose max-w-none">
                        <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                          {meeting.client_summary}
                        </div>
                      </div>
                      <div className="mt-6 pt-4 border-t border-gray-200">
                        <p className="text-sm text-gray-600">
                          Want to collaborate more closely on this project?{' '}
                          <button
                            onClick={generateShareableLink}
                            className="text-blue-600 hover:text-blue-500 font-medium"
                          >
                            Join KaiNote →
                          </button>
                        </p>
                      </div>
                    </div>

                    {/* Shareable Link */}
                    {shareableLink && (
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h3 className="text-sm font-medium text-blue-900 mb-2">Shareable Link</h3>
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={shareableLink}
                            readOnly
                            className="flex-1 text-sm bg-white border border-blue-200 rounded px-3 py-2"
                          />
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(shareableLink);
                              toast.success('Link copied to clipboard');
                            }}
                            className="px-3 py-2 text-sm bg-white border border-blue-200 rounded text-blue-700 hover:bg-blue-50"
                          >
                            Copy
                          </button>
                        </div>
                        <p className="text-xs text-blue-600 mt-2">
                          This link allows clients to view the summary and request project access.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <ShareIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No client summary yet</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {meeting.transcription_status === 'completed'
                        ? 'Generate a client-friendly summary to share with your client.'
                        : 'Client summary will be available after transcription is complete.'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
