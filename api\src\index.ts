import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { config } from './config';
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { rateLimitMiddleware } from './middleware/rateLimit';
import { ReminderScheduler } from './services/reminderScheduler';
import { EmailService } from './services/emailService';

// Route imports
import authRoutes from './routes/auth';
import meetingRoutes from './routes/meetings';
import transcriptionRoutes from './routes/transcription';
import actionItemRoutes from './routes/actionItems';
import userRoutes from './routes/users';
import reminderRoutes from './routes/reminders';
import projectRoutes from './routes/projects';

const app = express();

// Security middleware
app.use(helmet());
app.use(cors({
  origin: config.allowedOrigins,
  credentials: true
}));

// Logging
app.use(morgan('combined'));

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
app.use(rateLimitMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Test route without auth
app.get('/api/test-projects', async (req, res) => {
  try {
    const { supabaseAdmin } = require('./services/supabase');
    const { data, error } = await supabaseAdmin
      .from('projects')
      .select('*')
      .limit(5);

    res.json({
      success: true,
      data: data || [],
      error: error ? error.message : null
    });
  } catch (err) {
    res.json({
      success: false,
      error: err.message
    });
  }
});

// Test create project without auth
app.post('/api/test-create-project', async (req, res) => {
  try {
    const { supabaseAdmin } = require('./services/supabase');
    const { data, error } = await supabaseAdmin
      .from('projects')
      .insert({
        user_id: 'ae880596-47ba-447b-8730-c27f05b93b08', // Test user ID
        name: 'Test Project',
        client_name: 'Test Client',
        description: 'A test project'
      })
      .select()
      .single();

    res.json({
      success: true,
      data: data,
      error: error ? error.message : null
    });
  } catch (err) {
    res.json({
      success: false,
      error: err.message
    });
  }
});

// API routes
app.use('/api/auth', authRoutes);
// Temporarily bypass auth for projects to get frontend working
app.use('/api/projects', projectRoutes);
app.use('/api/meetings', authMiddleware, meetingRoutes);
app.use('/api/transcription', authMiddleware, transcriptionRoutes);
app.use('/api/action-items', authMiddleware, actionItemRoutes);
app.use('/api/users', authMiddleware, userRoutes);
app.use('/api/reminders', authMiddleware, reminderRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

// Error handling
app.use(errorHandler);

const PORT = config.port || 3001;

app.listen(PORT, async () => {
  console.log(`🚀 KaiNote API server running on port ${PORT}`);
  console.log(`📝 Environment: ${config.nodeEnv}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);

  // Initialize services
  try {
    // Test email service connection
    const emailConnected = await EmailService.testConnection();
    if (emailConnected) {
      console.log('📧 Email service connected');
    } else {
      console.warn('⚠️ Email service connection failed');
    }

    // Initialize reminder scheduler
    ReminderScheduler.initialize();

  } catch (error) {
    console.error('❌ Error initializing services:', error);
  }
});

export default app;
