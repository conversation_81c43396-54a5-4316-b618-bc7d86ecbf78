# KaiNote for Freelancers

Transform virtual client meetings into actionable results by recording, transcribing, extracting freelancer-specific action items, and delivering smart reminders.

## 🎯 Key Features

- **Smart Meeting Recording**: Chrome extension for browser-based meetings
- **AI Transcription**: Whisper API with speaker identification
- **Action Item Extraction**: GPT-4 powered freelancer-specific task extraction
- **Cost Calculator**: Meeting value assessment
- **Smart Reminders**: Automated deadline and task notifications
- **Client Summaries**: Professional meeting summaries for clients
- **Task Management**: Lightweight to-do interface

## 🏗️ Architecture

```
kainote/
├── chrome-extension/     # Chrome extension for meeting recording
├── web-app/             # Next.js dashboard and task management
├── api/                 # Node.js backend services
├── shared/              # Shared types and utilities
└── docs/                # Documentation
```

## 🚀 Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL (Supabase)
- **AI Services**: OpenAI GPT-4, Whisper API
- **Authentication**: Supabase Auth
- **Deployment**: Vercel (web), Railway (api)

## 💰 Pricing

- **Free**: 3 meetings/month, basic features
- **Pro**: $9.99/month, 300 minutes/month, advanced features

## 🛠️ Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run development servers

## 📝 License

MIT License - see LICENSE file for details
