{"name": "kainote-web-app", "version": "1.0.0", "description": "KaiNote web application for freelancers", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.38.5", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "next": "14.0.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "recharts": "^2.8.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}