import { Router } from 'express';
import { supabaseAdmin } from '../config/supabase';
import { AuthenticatedRequest } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { createError } from '../utils/errors';

const router = Router();

/**
 * GET /api/automation/rules
 * Get automation rules for the authenticated user
 */
router.get('/rules', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  try {
    const { data: rules, error } = await supabaseAdmin
      .from('automation_rules')
      .select('*')
      .eq('user_id', req.user.userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching automation rules:', error);
      throw createError('Failed to fetch automation rules', 500);
    }

    res.json({
      success: true,
      data: rules || []
    });

  } catch (error) {
    console.error('Automation rules fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/automation/rules
 * Create a new automation rule
 */
router.post('/rules', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    name,
    description,
    trigger_type,
    trigger_config,
    action_type,
    action_config,
    conditions = []
  } = req.body;

  if (!name || !trigger_type || !action_type || !trigger_config || !action_config) {
    throw createError('Name, trigger type, action type, trigger config, and action config are required', 400);
  }

  try {
    const { data: rule, error } = await supabaseAdmin
      .from('automation_rules')
      .insert({
        user_id: req.user.userId,
        name,
        description,
        trigger_type,
        trigger_config,
        action_type,
        action_config,
        conditions
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating automation rule:', error);
      throw createError('Failed to create automation rule', 500);
    }

    res.status(201).json({
      success: true,
      data: rule,
      message: 'Automation rule created successfully'
    });

  } catch (error) {
    console.error('Automation rule creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/automation/recurring-tasks
 * Get recurring tasks for the authenticated user
 */
router.get('/recurring-tasks', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  try {
    const { data: tasks, error } = await supabaseAdmin
      .from('recurring_tasks')
      .select(`
        *,
        project:projects(id, name, client_name),
        client:clients(id, name)
      `)
      .eq('user_id', req.user.userId)
      .order('next_due_date', { ascending: true });

    if (error) {
      console.error('Error fetching recurring tasks:', error);
      throw createError('Failed to fetch recurring tasks', 500);
    }

    res.json({
      success: true,
      data: tasks || []
    });

  } catch (error) {
    console.error('Recurring tasks fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/automation/recurring-tasks
 * Create a new recurring task
 */
router.post('/recurring-tasks', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    project_id,
    client_id,
    template_name,
    task_title,
    task_description,
    recurrence_pattern,
    recurrence_config = {},
    next_due_date,
    priority = 'medium',
    estimated_hours
  } = req.body;

  if (!template_name || !task_title || !recurrence_pattern || !next_due_date) {
    throw createError('Template name, task title, recurrence pattern, and next due date are required', 400);
  }

  try {
    const { data: task, error } = await supabaseAdmin
      .from('recurring_tasks')
      .insert({
        user_id: req.user.userId,
        project_id: project_id || null,
        client_id: client_id || null,
        template_name,
        task_title,
        task_description,
        recurrence_pattern,
        recurrence_config,
        next_due_date,
        priority,
        estimated_hours: estimated_hours ? parseFloat(estimated_hours) : null
      })
      .select(`
        *,
        project:projects(id, name, client_name),
        client:clients(id, name)
      `)
      .single();

    if (error) {
      console.error('Error creating recurring task:', error);
      throw createError('Failed to create recurring task', 500);
    }

    res.status(201).json({
      success: true,
      data: task,
      message: 'Recurring task created successfully'
    });

  } catch (error) {
    console.error('Recurring task creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/automation/workflow-templates
 * Get workflow templates for the authenticated user
 */
router.get('/workflow-templates', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { category } = req.query;

  try {
    let query = supabaseAdmin
      .from('workflow_templates')
      .select('*')
      .or(`user_id.eq.${req.user.userId},is_default.eq.true`)
      .eq('is_active', true)
      .order('usage_count', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    const { data: templates, error } = await query;

    if (error) {
      console.error('Error fetching workflow templates:', error);
      throw createError('Failed to fetch workflow templates', 500);
    }

    res.json({
      success: true,
      data: templates || []
    });

  } catch (error) {
    console.error('Workflow templates fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/automation/workflow-templates
 * Create a new workflow template
 */
router.post('/workflow-templates', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    name,
    description,
    category = 'general',
    steps
  } = req.body;

  if (!name || !steps || !Array.isArray(steps) || steps.length === 0) {
    throw createError('Name and steps array are required', 400);
  }

  try {
    const { data: template, error } = await supabaseAdmin
      .from('workflow_templates')
      .insert({
        user_id: req.user.userId,
        name,
        description,
        category,
        steps
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating workflow template:', error);
      throw createError('Failed to create workflow template', 500);
    }

    res.status(201).json({
      success: true,
      data: template,
      message: 'Workflow template created successfully'
    });

  } catch (error) {
    console.error('Workflow template creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/automation/workflow-instances
 * Get active workflow instances for the authenticated user
 */
router.get('/workflow-instances', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { status = 'active' } = req.query;

  try {
    const { data: instances, error } = await supabaseAdmin
      .from('workflow_instances')
      .select(`
        *,
        template:workflow_templates(id, name, category),
        project:projects(id, name, client_name),
        client:clients(id, name)
      `)
      .eq('user_id', req.user.userId)
      .eq('status', status)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching workflow instances:', error);
      throw createError('Failed to fetch workflow instances', 500);
    }

    res.json({
      success: true,
      data: instances || []
    });

  } catch (error) {
    console.error('Workflow instances fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/automation/workflow-instances
 * Create a new workflow instance from a template
 */
router.post('/workflow-instances', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    template_id,
    project_id,
    client_id,
    name,
    due_date
  } = req.body;

  if (!template_id || !name) {
    throw createError('Template ID and name are required', 400);
  }

  try {
    // Get the template
    const { data: template, error: templateError } = await supabaseAdmin
      .from('workflow_templates')
      .select('*')
      .eq('id', template_id)
      .or(`user_id.eq.${req.user.userId},is_default.eq.true`)
      .single();

    if (templateError || !template) {
      throw createError('Workflow template not found', 404);
    }

    // Create workflow instance
    const { data: instance, error } = await supabaseAdmin
      .from('workflow_instances')
      .insert({
        user_id: req.user.userId,
        template_id,
        project_id: project_id || null,
        client_id: client_id || null,
        name,
        total_steps: template.steps.length,
        due_date: due_date || null
      })
      .select(`
        *,
        template:workflow_templates(id, name, category),
        project:projects(id, name, client_name),
        client:clients(id, name)
      `)
      .single();

    if (error) {
      console.error('Error creating workflow instance:', error);
      throw createError('Failed to create workflow instance', 500);
    }

    // Increment template usage count
    await supabaseAdmin
      .from('workflow_templates')
      .update({ usage_count: template.usage_count + 1 })
      .eq('id', template_id);

    res.status(201).json({
      success: true,
      data: instance,
      message: 'Workflow instance created successfully'
    });

  } catch (error) {
    console.error('Workflow instance creation error:', error);
    throw error;
  }
}));

/**
 * PUT /api/automation/workflow-instances/:id/step
 * Update workflow instance step
 */
router.put('/workflow-instances/:id/step', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { id } = req.params;
  const { step_number, step_data, action = 'complete' } = req.body;

  try {
    // Get current instance
    const { data: instance, error: fetchError } = await supabaseAdmin
      .from('workflow_instances')
      .select('*')
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .single();

    if (fetchError || !instance) {
      throw createError('Workflow instance not found', 404);
    }

    let updateData: any = {};

    if (action === 'complete') {
      // Mark step as completed
      const completedSteps = [...(instance.completed_steps || [])];
      if (!completedSteps.includes(step_number)) {
        completedSteps.push(step_number);
      }

      updateData.completed_steps = completedSteps;
      updateData.current_step = Math.min(step_number + 1, instance.total_steps);

      // Check if workflow is complete
      if (completedSteps.length >= instance.total_steps) {
        updateData.status = 'completed';
        updateData.completed_at = new Date().toISOString();
      }
    } else if (action === 'update_data') {
      // Update step data
      updateData.step_data = {
        ...instance.step_data,
        [step_number]: step_data
      };
    }

    const { data: updatedInstance, error } = await supabaseAdmin
      .from('workflow_instances')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', req.user.userId)
      .select(`
        *,
        template:workflow_templates(id, name, category),
        project:projects(id, name, client_name),
        client:clients(id, name)
      `)
      .single();

    if (error) {
      console.error('Error updating workflow instance:', error);
      throw createError('Failed to update workflow instance', 500);
    }

    res.json({
      success: true,
      data: updatedInstance,
      message: 'Workflow step updated successfully'
    });

  } catch (error) {
    console.error('Workflow step update error:', error);
    throw error;
  }
}));

/**
 * GET /api/automation/followups
 * Get automated follow-ups for the authenticated user
 */
router.get('/followups', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const { status, target_type } = req.query;

  try {
    let query = supabaseAdmin
      .from('automated_followups')
      .select(`
        *,
        email_template:email_templates(id, name, subject)
      `)
      .eq('user_id', req.user.userId)
      .order('scheduled_date', { ascending: true });

    if (status) {
      query = query.eq('status', status);
    }
    if (target_type) {
      query = query.eq('target_type', target_type);
    }

    const { data: followups, error } = await query;

    if (error) {
      console.error('Error fetching automated followups:', error);
      throw createError('Failed to fetch automated followups', 500);
    }

    res.json({
      success: true,
      data: followups || []
    });

  } catch (error) {
    console.error('Automated followups fetch error:', error);
    throw error;
  }
}));

/**
 * POST /api/automation/followups
 * Create a new automated follow-up
 */
router.post('/followups', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  const {
    target_type,
    target_id,
    followup_type,
    trigger_condition,
    email_template_id,
    custom_message,
    scheduled_date
  } = req.body;

  if (!target_type || !target_id || !followup_type || !trigger_condition) {
    throw createError('Target type, target ID, followup type, and trigger condition are required', 400);
  }

  try {
    const { data: followup, error } = await supabaseAdmin
      .from('automated_followups')
      .insert({
        user_id: req.user.userId,
        target_type,
        target_id,
        followup_type,
        trigger_condition,
        email_template_id: email_template_id || null,
        custom_message,
        scheduled_date: scheduled_date || null
      })
      .select(`
        *,
        email_template:email_templates(id, name, subject)
      `)
      .single();

    if (error) {
      console.error('Error creating automated followup:', error);
      throw createError('Failed to create automated followup', 500);
    }

    res.status(201).json({
      success: true,
      data: followup,
      message: 'Automated follow-up created successfully'
    });

  } catch (error) {
    console.error('Automated followup creation error:', error);
    throw error;
  }
}));

/**
 * GET /api/automation/dashboard
 * Get automation dashboard data
 */
router.get('/dashboard', asyncHandler(async (req: AuthenticatedRequest, res) => {
  if (!req.user) {
    throw createError('User not authenticated', 401);
  }

  try {
    // Get automation rules count
    const { data: rules } = await supabaseAdmin
      .from('automation_rules')
      .select('id, is_active')
      .eq('user_id', req.user.userId);

    // Get active workflow instances
    const { data: workflows } = await supabaseAdmin
      .from('workflow_instances')
      .select('id, status, current_step, total_steps')
      .eq('user_id', req.user.userId)
      .eq('status', 'active');

    // Get upcoming recurring tasks
    const { data: recurringTasks } = await supabaseAdmin
      .from('recurring_tasks')
      .select('id, next_due_date, task_title')
      .eq('user_id', req.user.userId)
      .eq('is_active', true)
      .lte('next_due_date', new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('next_due_date', { ascending: true });

    // Get pending follow-ups
    const { data: followups } = await supabaseAdmin
      .from('automated_followups')
      .select('id, target_type, followup_type, scheduled_date')
      .eq('user_id', req.user.userId)
      .eq('status', 'scheduled')
      .lte('scheduled_date', new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('scheduled_date', { ascending: true });

    // Get recent executions
    const { data: executions } = await supabaseAdmin
      .from('automation_executions')
      .select(`
        id, execution_status, executed_at,
        rule:automation_rules(id, name)
      `)
      .eq('user_id', req.user.userId)
      .order('executed_at', { ascending: false })
      .limit(10);

    const activeRules = rules?.filter(rule => rule.is_active).length || 0;
    const totalRules = rules?.length || 0;

    res.json({
      success: true,
      data: {
        summary: {
          totalRules,
          activeRules,
          activeWorkflows: workflows?.length || 0,
          upcomingTasks: recurringTasks?.length || 0,
          pendingFollowups: followups?.length || 0
        },
        activeWorkflows: workflows || [],
        upcomingTasks: recurringTasks || [],
        pendingFollowups: followups || [],
        recentExecutions: executions || []
      }
    });

  } catch (error) {
    console.error('Automation dashboard error:', error);
    throw error;
  }
}));

export default router;
