import cron from 'node-cron';
import { DatabaseService } from './supabase';
import { EmailService } from './emailService';

export class ReminderScheduler {
  private static isInitialized = false;

  /**
   * Initialize all scheduled tasks
   */
  static initialize(): void {
    if (this.isInitialized) {
      console.log('Reminder scheduler already initialized');
      return;
    }

    console.log('Initializing reminder scheduler...');

    // Check for deadline reminders every hour
    cron.schedule('0 * * * *', async () => {
      console.log('Running deadline reminder check...');
      await this.checkDeadlineReminders();
    });

    // Check for overdue tasks every day at 9 AM
    cron.schedule('0 9 * * *', async () => {
      console.log('Running overdue task check...');
      await this.checkOverdueTasks();
    });

    // Send weekly digest every Monday at 9 AM
    cron.schedule('0 9 * * 1', async () => {
      console.log('Sending weekly digests...');
      await this.sendWeeklyDigests();
    });

    this.isInitialized = true;
    console.log('✅ Reminder scheduler initialized successfully');
  }

  /**
   * Check for upcoming deadlines and send reminders
   */
  static async checkDeadlineReminders(): Promise<void> {
    try {
      // Get all pending action items with deadlines
      const allUsers = await this.getAllActiveUsers();
      
      for (const user of allUsers) {
        const actionItems = await DatabaseService.getActionItemsByUserId(user.id, 'pending');
        
        const itemsNeedingReminders = actionItems.filter(item => {
          if (!item.deadline) return false;
          
          const deadline = new Date(item.deadline);
          const now = new Date();
          const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);
          
          // Send reminder if deadline is in 24 hours or 2 hours
          return (hoursUntilDeadline <= 24 && hoursUntilDeadline > 23) || 
                 (hoursUntilDeadline <= 2 && hoursUntilDeadline > 1);
        });

        // Send individual reminders
        for (const item of itemsNeedingReminders) {
          await EmailService.sendDeadlineReminder(user.email, user.name, {
            task: item.task,
            deadline: item.deadline!,
            priority: item.priority,
            context: item.context
          });

          console.log(`Sent deadline reminder to ${user.email} for task: ${item.task}`);
        }
      }

    } catch (error) {
      console.error('Error checking deadline reminders:', error);
    }
  }

  /**
   * Check for overdue tasks and send notifications
   */
  static async checkOverdueTasks(): Promise<void> {
    try {
      const allUsers = await this.getAllActiveUsers();
      
      for (const user of allUsers) {
        const actionItems = await DatabaseService.getActionItemsByUserId(user.id, 'pending');
        
        const overdueItems = actionItems.filter(item => {
          if (!item.deadline) return false;
          return new Date(item.deadline) < new Date();
        });

        if (overdueItems.length > 0) {
          await EmailService.sendOverdueNotification(user.email, user.name, overdueItems.map(item => ({
            task: item.task,
            deadline: item.deadline!,
            priority: item.priority,
            context: item.context
          })));

          console.log(`Sent overdue notification to ${user.email} for ${overdueItems.length} items`);
        }
      }

    } catch (error) {
      console.error('Error checking overdue tasks:', error);
    }
  }

  /**
   * Send weekly digest emails
   */
  static async sendWeeklyDigests(): Promise<void> {
    try {
      const allUsers = await this.getAllActiveUsers();
      
      for (const user of allUsers) {
        const digestData = await this.generateWeeklyDigestData(user.id);
        
        await EmailService.sendWeeklyDigest(user.email, user.name, digestData);
        console.log(`Sent weekly digest to ${user.email}`);
      }

    } catch (error) {
      console.error('Error sending weekly digests:', error);
    }
  }

  /**
   * Generate weekly digest data for a user
   */
  private static async generateWeeklyDigestData(userId: string) {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    // Get all action items
    const allActionItems = await DatabaseService.getActionItemsByUserId(userId);
    
    // Get meetings from this week
    const allMeetings = await DatabaseService.getMeetingsByUserId(userId, 100);
    const meetingsThisWeek = allMeetings.filter(meeting => 
      new Date(meeting.created_at) >= oneWeekAgo
    );

    // Calculate completed tasks this week
    const completedTasks = allActionItems.filter(item => 
      item.status === 'completed' && 
      new Date(item.updated_at) >= oneWeekAgo
    ).length;

    // Get pending and overdue items
    const pendingTasks = allActionItems.filter(item => item.status === 'pending').length;
    const overdueItems = allActionItems.filter(item => {
      if (!item.deadline || item.status !== 'pending') return false;
      return new Date(item.deadline) < new Date();
    }).length;

    // Get upcoming deadlines (next 7 days)
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    
    const upcomingDeadlines = allActionItems
      .filter(item => {
        if (!item.deadline || item.status !== 'pending') return false;
        const deadline = new Date(item.deadline);
        return deadline >= new Date() && deadline <= nextWeek;
      })
      .sort((a, b) => new Date(a.deadline!).getTime() - new Date(b.deadline!).getTime())
      .map(item => ({
        task: item.task,
        deadline: item.deadline!,
        priority: item.priority
      }));

    return {
      completedTasks,
      pendingTasks,
      overdueItems,
      meetingsThisWeek: meetingsThisWeek.length,
      upcomingDeadlines
    };
  }

  /**
   * Get all active users (users who have used the service recently)
   */
  private static async getAllActiveUsers() {
    // In a real implementation, you might want to filter for active users
    // For now, we'll get all users but you could add criteria like:
    // - Users who have logged in within the last 30 days
    // - Users who have meetings in the last month
    // - Users who haven't unsubscribed from notifications
    
    try {
      // This is a simplified approach - in production you'd want a more efficient query
      const { data: users, error } = await DatabaseService.supabaseAdmin
        .from('users')
        .select('id, email, name, subscription_tier')
        .limit(1000); // Reasonable limit for batch processing

      if (error) throw error;
      return users || [];
      
    } catch (error) {
      console.error('Error fetching active users:', error);
      return [];
    }
  }

  /**
   * Schedule a custom reminder for a specific action item
   */
  static async scheduleCustomReminder(
    userId: string,
    actionItemId: string,
    reminderTime: Date,
    reminderType: 'deadline' | 'custom' = 'custom'
  ): Promise<void> {
    try {
      // Store the reminder in the database
      const { data, error } = await DatabaseService.supabaseAdmin
        .from('reminders')
        .insert({
          user_id: userId,
          action_item_id: actionItemId,
          reminder_type: reminderType,
          scheduled_for: reminderTime.toISOString()
        });

      if (error) throw error;
      
      console.log(`Scheduled ${reminderType} reminder for user ${userId} at ${reminderTime}`);
      
    } catch (error) {
      console.error('Error scheduling custom reminder:', error);
      throw error;
    }
  }

  /**
   * Process pending reminders (for custom scheduled reminders)
   */
  static async processPendingReminders(): Promise<void> {
    try {
      const now = new Date();
      
      // Get all pending reminders that should be sent now
      const { data: pendingReminders, error } = await DatabaseService.supabaseAdmin
        .from('reminders')
        .select(`
          *,
          users!inner(email, name),
          action_items!inner(task, deadline, priority, context)
        `)
        .lte('scheduled_for', now.toISOString())
        .is('sent_at', null);

      if (error) throw error;

      for (const reminder of pendingReminders || []) {
        try {
          // Send the reminder email
          await EmailService.sendDeadlineReminder(
            reminder.users.email,
            reminder.users.name,
            {
              task: reminder.action_items.task,
              deadline: reminder.action_items.deadline,
              priority: reminder.action_items.priority,
              context: reminder.action_items.context
            }
          );

          // Mark reminder as sent
          await DatabaseService.supabaseAdmin
            .from('reminders')
            .update({ sent_at: now.toISOString() })
            .eq('id', reminder.id);

          console.log(`Sent custom reminder ${reminder.id}`);

        } catch (error) {
          console.error(`Failed to send reminder ${reminder.id}:`, error);
        }
      }

    } catch (error) {
      console.error('Error processing pending reminders:', error);
    }
  }

  /**
   * Stop all scheduled tasks (for graceful shutdown)
   */
  static destroy(): void {
    cron.destroy();
    this.isInitialized = false;
    console.log('Reminder scheduler stopped');
  }
}
