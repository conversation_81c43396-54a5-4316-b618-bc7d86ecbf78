import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

// Create axios instance
export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.error || error.message || 'An error occurred';
    
    // Handle specific error cases
    if (error.response?.status === 401) {
      // Unauthorized - clear auth and redirect to login
      Cookies.remove('auth-token');
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/signin';
      }
    } else if (error.response?.status === 429) {
      // Rate limited
      toast.error('Too many requests. Please try again later.');
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('Server error. Please try again later.');
    } else if (!error.response) {
      // Network error
      toast.error('Network error. Please check your connection.');
    }
    
    return Promise.reject(error);
  }
);

// API helper functions
export const apiHelpers = {
  // Auth
  signIn: (email: string, password: string) =>
    api.post('/auth/signin', { email, password }),
  
  signUp: (email: string, password: string, name: string) =>
    api.post('/auth/signup', { email, password, name }),
  
  verifyToken: () =>
    api.post('/auth/verify'),
  
  // User
  getProfile: () =>
    api.get('/users/profile'),
  
  updateProfile: (updates: any) =>
    api.put('/users/profile', updates),
  
  getUsage: () =>
    api.get('/users/usage'),
  
  getDashboard: () =>
    api.get('/users/dashboard'),
  
  // Meetings
  getMeetings: (limit?: number) =>
    api.get('/meetings', { params: { limit } }),
  
  getMeeting: (id: string) =>
    api.get(`/meetings/${id}`),
  
  uploadMeeting: (formData: FormData) =>
    api.post('/meetings/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      timeout: 300000, // 5 minutes for file upload
    }),
  
  deleteMeeting: (id: string) =>
    api.delete(`/meetings/${id}`),
  
  // Action Items
  getActionItems: (params?: { status?: string; meeting_id?: string }) =>
    api.get('/action-items', { params }),
  
  updateActionItem: (id: string, updates: any) =>
    api.put(`/action-items/${id}`, updates),
  
  getActionItemStats: () =>
    api.get('/action-items/stats'),
  
  // Transcription
  getTranscription: (meetingId: string) =>
    api.get(`/transcription/${meetingId}`),
  
  searchTranscription: (meetingId: string, query: string) =>
    api.get(`/transcription/${meetingId}/search`, { params: { q: query } }),
  
  // Reminders
  getReminders: () =>
    api.get('/reminders'),
  
  scheduleReminder: (actionItemId: string, reminderTime: string, reminderType?: string) =>
    api.post('/reminders/schedule', { actionItemId, reminderTime, reminderType }),
  
  cancelReminder: (id: string) =>
    api.delete(`/reminders/${id}`),
  
  sendTestEmail: (type: string) =>
    api.post('/reminders/test', { type }),
};

export default api;
