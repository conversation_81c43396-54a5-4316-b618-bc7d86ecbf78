// Date and time utilities
export const formatDate = (date: string | Date): string => {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const formatDateTime = (date: string | Date): string => {
  const d = new Date(date);
  return d.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours === 0) {
    return `${mins}m`;
  }
  
  return `${hours}h ${mins}m`;
};

// Meeting platform detection
export const detectMeetingPlatform = (url: string): 'google-meet' | 'zoom' | 'teams' | 'other' => {
  if (url.includes('meet.google.com')) return 'google-meet';
  if (url.includes('zoom.us') || url.includes('zoom.com')) return 'zoom';
  if (url.includes('teams.microsoft.com')) return 'teams';
  return 'other';
};

// Priority helpers
export const getPriorityColor = (priority: 'low' | 'medium' | 'high'): string => {
  switch (priority) {
    case 'low': return 'text-green-600 bg-green-100';
    case 'medium': return 'text-yellow-600 bg-yellow-100';
    case 'high': return 'text-red-600 bg-red-100';
    default: return 'text-gray-600 bg-gray-100';
  }
};

// Status helpers
export const getStatusColor = (status: 'pending' | 'in_progress' | 'completed' | 'cancelled'): string => {
  switch (status) {
    case 'pending': return 'text-gray-600 bg-gray-100';
    case 'in_progress': return 'text-blue-600 bg-blue-100';
    case 'completed': return 'text-green-600 bg-green-100';
    case 'cancelled': return 'text-red-600 bg-red-100';
    default: return 'text-gray-600 bg-gray-100';
  }
};

// Validation helpers
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Cost calculation
export const calculateMeetingCost = (durationMinutes: number, hourlyRate: number = 75): number => {
  return (durationMinutes / 60) * hourlyRate;
};

// Text processing
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

export const extractKeywords = (text: string): string[] => {
  // Simple keyword extraction - can be enhanced with NLP
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3);
  
  // Remove common stop words
  const stopWords = ['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'were', 'said'];
  return words.filter(word => !stopWords.includes(word));
};

// API helpers
export const createApiResponse = <T>(success: boolean, data?: T, error?: string, message?: string) => {
  return {
    success,
    data,
    error,
    message,
  };
};

// Chrome extension helpers
export const isExtensionContext = (): boolean => {
  return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
};

export const sendExtensionMessage = (message: any): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!isExtensionContext()) {
      reject(new Error('Not in extension context'));
      return;
    }
    
    chrome.runtime.sendMessage(message, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(response);
      }
    });
  });
};
