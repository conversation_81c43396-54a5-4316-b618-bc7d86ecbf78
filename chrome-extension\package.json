{"name": "kainote-chrome-extension", "version": "1.0.0", "description": "KaiNote Chrome Extension for meeting recording", "scripts": {"build": "webpack --mode=production", "dev": "webpack --mode=development --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/chrome": "^0.0.254", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.56.0", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@kainote/shared": "workspace:*"}}