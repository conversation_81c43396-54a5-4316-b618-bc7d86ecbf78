import nodemailer from 'nodemailer';
import { config } from '../config';

export class EmailService {
  private static transporter = nodemailer.createTransporter({
    host: config.smtpHost,
    port: config.smtpPort,
    secure: config.smtpPort === 465, // true for 465, false for other ports
    auth: {
      user: config.smtpUser,
      pass: config.smtpPass,
    },
  });

  /**
   * Send deadline reminder email
   */
  static async sendDeadlineReminder(
    userEmail: string,
    userName: string,
    actionItem: {
      task: string;
      deadline: string;
      priority: string;
      context?: string;
    }
  ): Promise<void> {
    const deadlineDate = new Date(actionItem.deadline);
    const formattedDeadline = deadlineDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const priorityEmoji = {
      high: '🔴',
      medium: '🟡',
      low: '🟢'
    }[actionItem.priority] || '⚪';

    const subject = `${priorityEmoji} Reminder: ${actionItem.task} - Due ${formattedDeadline}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>KaiNote Reminder</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3b82f6; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .task-card { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 20px 0; }
          .priority-high { border-left-color: #ef4444; }
          .priority-medium { border-left-color: #f59e0b; }
          .priority-low { border-left-color: #10b981; }
          .deadline { font-size: 18px; font-weight: 600; color: #1f2937; margin-bottom: 10px; }
          .task-title { font-size: 16px; font-weight: 500; margin-bottom: 10px; }
          .context { color: #6b7280; font-size: 14px; }
          .cta { text-align: center; margin: 30px 0; }
          .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
          .footer { text-align: center; color: #6b7280; font-size: 12px; margin-top: 30px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📝 KaiNote Reminder</h1>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>This is a friendly reminder about an upcoming deadline:</p>
            
            <div class="task-card priority-${actionItem.priority}">
              <div class="deadline">${priorityEmoji} Due: ${formattedDeadline}</div>
              <div class="task-title">${actionItem.task}</div>
              ${actionItem.context ? `<div class="context">${actionItem.context}</div>` : ''}
            </div>

            <p>Don't let this slip through the cracks! Staying on top of your commitments helps maintain excellent client relationships.</p>

            <div class="cta">
              <a href="${process.env.WEB_APP_URL}/tasks" class="button">View All Tasks</a>
            </div>

            <p>Best regards,<br>The KaiNote Team</p>
          </div>
          <div class="footer">
            <p>You're receiving this because you have deadline reminders enabled in KaiNote.</p>
            <p><a href="${process.env.WEB_APP_URL}/settings">Manage notification preferences</a></p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      KaiNote Reminder
      
      Hi ${userName},
      
      This is a reminder about an upcoming deadline:
      
      Task: ${actionItem.task}
      Due: ${formattedDeadline}
      Priority: ${actionItem.priority.toUpperCase()}
      ${actionItem.context ? `Context: ${actionItem.context}` : ''}
      
      View all your tasks: ${process.env.WEB_APP_URL}/tasks
      
      Best regards,
      The KaiNote Team
    `;

    await this.sendEmail(userEmail, subject, text, html);
  }

  /**
   * Send overdue task notification
   */
  static async sendOverdueNotification(
    userEmail: string,
    userName: string,
    overdueItems: Array<{
      task: string;
      deadline: string;
      priority: string;
      context?: string;
    }>
  ): Promise<void> {
    const subject = `⚠️ ${overdueItems.length} Overdue Task${overdueItems.length > 1 ? 's' : ''} - Action Required`;

    const tasksList = overdueItems.map(item => {
      const daysOverdue = Math.ceil((Date.now() - new Date(item.deadline).getTime()) / (1000 * 60 * 60 * 24));
      const priorityEmoji = {
        high: '🔴',
        medium: '🟡',
        low: '🟢'
      }[item.priority] || '⚪';
      
      return `
        <div class="task-card priority-${item.priority}">
          <div class="overdue-badge">${priorityEmoji} ${daysOverdue} day${daysOverdue > 1 ? 's' : ''} overdue</div>
          <div class="task-title">${item.task}</div>
          <div class="deadline">Was due: ${new Date(item.deadline).toLocaleDateString()}</div>
          ${item.context ? `<div class="context">${item.context}</div>` : ''}
        </div>
      `;
    }).join('');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Overdue Tasks - KaiNote</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #ef4444; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .task-card { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #ef4444; margin: 15px 0; }
          .overdue-badge { background: #fef2f2; color: #dc2626; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600; margin-bottom: 10px; display: inline-block; }
          .task-title { font-size: 16px; font-weight: 500; margin-bottom: 5px; }
          .deadline { color: #6b7280; font-size: 14px; margin-bottom: 5px; }
          .context { color: #6b7280; font-size: 14px; }
          .cta { text-align: center; margin: 30px 0; }
          .button { display: inline-block; background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
          .footer { text-align: center; color: #6b7280; font-size: 12px; margin-top: 30px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⚠️ Overdue Tasks</h1>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>You have ${overdueItems.length} overdue task${overdueItems.length > 1 ? 's' : ''} that need${overdueItems.length === 1 ? 's' : ''} your attention:</p>
            
            ${tasksList}

            <p><strong>Don't let overdue tasks damage your client relationships!</strong> Consider reaching out to your clients to update them on the status and set new expectations.</p>

            <div class="cta">
              <a href="${process.env.WEB_APP_URL}/tasks?filter=overdue" class="button">Review Overdue Tasks</a>
            </div>

            <p>Best regards,<br>The KaiNote Team</p>
          </div>
          <div class="footer">
            <p>You're receiving this because you have overdue task notifications enabled.</p>
            <p><a href="${process.env.WEB_APP_URL}/settings">Manage notification preferences</a></p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Overdue Tasks - KaiNote
      
      Hi ${userName},
      
      You have ${overdueItems.length} overdue task${overdueItems.length > 1 ? 's' : ''}:
      
      ${overdueItems.map(item => `
      - ${item.task}
        Was due: ${new Date(item.deadline).toLocaleDateString()}
        Priority: ${item.priority.toUpperCase()}
      `).join('\n')}
      
      Review your overdue tasks: ${process.env.WEB_APP_URL}/tasks?filter=overdue
      
      Best regards,
      The KaiNote Team
    `;

    await this.sendEmail(userEmail, subject, text, html);
  }

  /**
   * Send weekly digest email
   */
  static async sendWeeklyDigest(
    userEmail: string,
    userName: string,
    digestData: {
      completedTasks: number;
      pendingTasks: number;
      overdueItems: number;
      meetingsThisWeek: number;
      upcomingDeadlines: Array<{
        task: string;
        deadline: string;
        priority: string;
      }>;
    }
  ): Promise<void> {
    const subject = `📊 Your Weekly KaiNote Summary`;

    const upcomingList = digestData.upcomingDeadlines.slice(0, 5).map(item => {
      const priorityEmoji = {
        high: '🔴',
        medium: '🟡',
        low: '🟢'
      }[item.priority] || '⚪';
      
      return `
        <li style="margin-bottom: 8px;">
          ${priorityEmoji} ${item.task} - Due ${new Date(item.deadline).toLocaleDateString()}
        </li>
      `;
    }).join('');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Weekly Summary - KaiNote</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3b82f6; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .stats-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
          .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; }
          .stat-number { font-size: 24px; font-weight: 700; color: #1f2937; }
          .stat-label { color: #6b7280; font-size: 14px; }
          .upcoming-section { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .cta { text-align: center; margin: 30px 0; }
          .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
          .footer { text-align: center; color: #6b7280; font-size: 12px; margin-top: 30px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📊 Weekly Summary</h1>
            <p>Your productivity at a glance</p>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>Here's your weekly KaiNote summary:</p>
            
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">${digestData.completedTasks}</div>
                <div class="stat-label">Tasks Completed</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${digestData.meetingsThisWeek}</div>
                <div class="stat-label">Meetings Recorded</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${digestData.pendingTasks}</div>
                <div class="stat-label">Pending Tasks</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">${digestData.overdueItems}</div>
                <div class="stat-label">Overdue Items</div>
              </div>
            </div>

            ${digestData.upcomingDeadlines.length > 0 ? `
              <div class="upcoming-section">
                <h3>🗓️ Upcoming Deadlines</h3>
                <ul style="padding-left: 20px;">
                  ${upcomingList}
                </ul>
              </div>
            ` : ''}

            <div class="cta">
              <a href="${process.env.WEB_APP_URL}/dashboard" class="button">View Dashboard</a>
            </div>

            <p>Keep up the great work! Consistent follow-through is what sets successful freelancers apart.</p>

            <p>Best regards,<br>The KaiNote Team</p>
          </div>
          <div class="footer">
            <p>You're receiving this weekly summary because you have digest emails enabled.</p>
            <p><a href="${process.env.WEB_APP_URL}/settings">Manage notification preferences</a></p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Weekly KaiNote Summary
      
      Hi ${userName},
      
      Here's your weekly summary:
      
      📈 This Week:
      - ${digestData.completedTasks} tasks completed
      - ${digestData.meetingsThisWeek} meetings recorded
      - ${digestData.pendingTasks} pending tasks
      - ${digestData.overdueItems} overdue items
      
      ${digestData.upcomingDeadlines.length > 0 ? `
      🗓️ Upcoming Deadlines:
      ${digestData.upcomingDeadlines.slice(0, 5).map(item => 
        `- ${item.task} - Due ${new Date(item.deadline).toLocaleDateString()}`
      ).join('\n')}
      ` : ''}
      
      View your dashboard: ${process.env.WEB_APP_URL}/dashboard
      
      Best regards,
      The KaiNote Team
    `;

    await this.sendEmail(userEmail, subject, text, html);
  }

  /**
   * Send basic email
   */
  private static async sendEmail(
    to: string,
    subject: string,
    text: string,
    html?: string
  ): Promise<void> {
    try {
      const mailOptions = {
        from: `"KaiNote" <${config.smtpUser}>`,
        to,
        subject,
        text,
        html
      };

      const info = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', info.messageId);
      
    } catch (error) {
      console.error('Failed to send email:', error);
      throw new Error(`Email delivery failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Test email configuration
   */
  static async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('Email service connection verified');
      return true;
    } catch (error) {
      console.error('Email service connection failed:', error);
      return false;
    }
  }
}
