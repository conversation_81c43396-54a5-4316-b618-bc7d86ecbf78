// Content script for KaiNote Chrome Extension
// Runs on meeting platform pages

interface RecordingSession {
  mediaRecorder?: MediaRecorder;
  audioChunks: Blob[];
  isRecording: boolean;
  startTime?: number;
}

let recordingSession: RecordingSession = {
  audioChunks: [],
  isRecording: false
};

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Content script received message:', message);

  switch (message.type) {
    case 'START_RECORDING_CONFIRMED':
      handleStartRecording(message.payload)
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'STOP_RECORDING_CONFIRMED':
      handleStopRecording()
        .then(sendResponse)
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    default:
      sendResponse({ success: false, error: 'Unknown message type' });
  }
});

async function handleStartRecording(payload: { streamId: string; platform: string }): Promise<void> {
  try {
    // Get media stream using the streamId from background script
    const stream = await navigator.mediaDevices.getUserMedia({
      video: false,
      audio: {
        mandatory: {
          chromeMediaSource: 'tab',
          chromeMediaSourceId: payload.streamId
        }
      } as any
    });

    // Create MediaRecorder
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus'
    });

    recordingSession.mediaRecorder = mediaRecorder;
    recordingSession.audioChunks = [];
    recordingSession.startTime = Date.now();

    // Handle data available
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordingSession.audioChunks.push(event.data);
      }
    };

    // Handle recording stop
    mediaRecorder.onstop = async () => {
      console.log('Recording stopped, processing audio...');
      await processRecordedAudio();
    };

    // Start recording
    mediaRecorder.start(1000); // Collect data every second
    recordingSession.isRecording = true;

    // Show recording indicator
    showRecordingIndicator();

    console.log('Recording started successfully');

  } catch (error) {
    console.error('Error starting recording:', error);
    throw error;
  }
}

async function handleStopRecording(): Promise<any> {
  if (!recordingSession.isRecording || !recordingSession.mediaRecorder) {
    throw new Error('No recording in progress');
  }

  try {
    // Stop the media recorder
    recordingSession.mediaRecorder.stop();
    
    // Stop all tracks to release the stream
    recordingSession.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    
    recordingSession.isRecording = false;

    // Hide recording indicator
    hideRecordingIndicator();

    const duration = recordingSession.startTime 
      ? Math.floor((Date.now() - recordingSession.startTime) / 1000 / 60)
      : 0;

    return {
      success: true,
      data: { duration }
    };

  } catch (error) {
    console.error('Error stopping recording:', error);
    throw error;
  }
}

async function processRecordedAudio(): Promise<void> {
  if (recordingSession.audioChunks.length === 0) {
    console.warn('No audio data to process');
    return;
  }

  try {
    // Create blob from audio chunks
    const audioBlob = new Blob(recordingSession.audioChunks, { type: 'audio/webm' });
    
    // Get meeting info
    const meetingInfo = extractMeetingInfo();
    
    // Upload to API
    await uploadAudioToAPI(audioBlob, meetingInfo);

  } catch (error) {
    console.error('Error processing recorded audio:', error);
  }
}

function extractMeetingInfo() {
  const url = window.location.href;
  const title = document.title;
  
  // Platform-specific extraction
  let meetingId = '';
  let participants: string[] = [];

  if (url.includes('meet.google.com')) {
    meetingId = url.split('/').pop() || '';
    // Try to extract participant names from Google Meet
    const participantElements = document.querySelectorAll('[data-self-name], [data-participant-id]');
    participants = Array.from(participantElements).map(el => el.textContent || '').filter(Boolean);
  } else if (url.includes('zoom.us') || url.includes('zoom.com')) {
    // Extract Zoom meeting info
    const meetingIdMatch = url.match(/\/j\/(\d+)/);
    meetingId = meetingIdMatch ? meetingIdMatch[1] : '';
  } else if (url.includes('teams.microsoft.com')) {
    // Extract Teams meeting info
    meetingId = url.split('?')[0].split('/').pop() || '';
  }

  return {
    title: title.replace(' - Google Meet', '').replace(' - Zoom', '').replace(' - Microsoft Teams', ''),
    meetingId,
    participants,
    url,
    platform: detectMeetingPlatform(url),
    duration: recordingSession.startTime ? Math.floor((Date.now() - recordingSession.startTime) / 1000 / 60) : 0
  };
}

function detectMeetingPlatform(url: string): string {
  if (url.includes('meet.google.com')) return 'google-meet';
  if (url.includes('zoom.us') || url.includes('zoom.com')) return 'zoom';
  if (url.includes('teams.microsoft.com')) return 'teams';
  return 'other';
}

async function uploadAudioToAPI(audioBlob: Blob, meetingInfo: any): Promise<void> {
  try {
    // Get auth token from storage
    const result = await chrome.storage.local.get(['authToken']);
    
    if (!result.authToken) {
      console.error('No auth token found');
      return;
    }

    // Create FormData
    const formData = new FormData();
    formData.append('audio', audioBlob, 'meeting-recording.webm');
    formData.append('meetingInfo', JSON.stringify(meetingInfo));

    // Upload to API
    const API_BASE_URL = process.env.NODE_ENV === 'production' 
      ? 'https://api.kainote.com' 
      : 'http://localhost:3001';

    const response = await fetch(`${API_BASE_URL}/api/meetings/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${result.authToken}`
      },
      body: formData
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Audio uploaded successfully:', result);
      
      // Show success notification
      showNotification('Meeting recorded and uploaded successfully!', 'success');
    } else {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

  } catch (error) {
    console.error('Error uploading audio:', error);
    showNotification('Failed to upload recording. Please try again.', 'error');
  }
}

function showRecordingIndicator(): void {
  // Remove existing indicator
  hideRecordingIndicator();

  // Create recording indicator
  const indicator = document.createElement('div');
  indicator.id = 'kainote-recording-indicator';
  indicator.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: #dc2626;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      gap: 8px;
    ">
      <div style="
        width: 8px;
        height: 8px;
        background: white;
        border-radius: 50%;
        animation: pulse 1.5s infinite;
      "></div>
      Recording with KaiNote
    </div>
    <style>
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
    </style>
  `;

  document.body.appendChild(indicator);
}

function hideRecordingIndicator(): void {
  const indicator = document.getElementById('kainote-recording-indicator');
  if (indicator) {
    indicator.remove();
  }
}

function showNotification(message: string, type: 'success' | 'error'): void {
  const notification = document.createElement('div');
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#059669' : '#dc2626'};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 300px;
    ">
      ${message}
    </div>
  `;

  document.body.appendChild(notification);

  // Remove after 5 seconds
  setTimeout(() => {
    notification.remove();
  }, 5000);
}

// Initialize content script
console.log('KaiNote content script loaded on:', window.location.href);
