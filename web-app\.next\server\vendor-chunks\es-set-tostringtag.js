"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/es-set-tostringtag";
exports.ids = ["vendor-chunks/es-set-tostringtag"];
exports.modules = {

/***/ "(ssr)/./node_modules/es-set-tostringtag/index.js":
/*!**************************************************!*\
  !*** ./node_modules/es-set-tostringtag/index.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(ssr)/./node_modules/get-intrinsic/index.js\");\nvar $defineProperty = GetIntrinsic(\"%Object.defineProperty%\", true);\nvar hasToStringTag = __webpack_require__(/*! has-tostringtag/shams */ \"(ssr)/./node_modules/has-tostringtag/shams.js\")();\nvar hasOwn = __webpack_require__(/*! hasown */ \"(ssr)/./node_modules/hasown/index.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(ssr)/./node_modules/es-errors/type.js\");\nvar toStringTag = hasToStringTag ? Symbol.toStringTag : null;\n/** @type {import('.')} */ module.exports = function setToStringTag(object, value) {\n    var overrideIfSet = arguments.length > 2 && !!arguments[2] && arguments[2].force;\n    var nonConfigurable = arguments.length > 2 && !!arguments[2] && arguments[2].nonConfigurable;\n    if (typeof overrideIfSet !== \"undefined\" && typeof overrideIfSet !== \"boolean\" || typeof nonConfigurable !== \"undefined\" && typeof nonConfigurable !== \"boolean\") {\n        throw new $TypeError(\"if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans\");\n    }\n    if (toStringTag && (overrideIfSet || !hasOwn(object, toStringTag))) {\n        if ($defineProperty) {\n            $defineProperty(object, toStringTag, {\n                configurable: !nonConfigurable,\n                enumerable: false,\n                value: value,\n                writable: false\n            });\n        } else {\n            object[toStringTag] = value; // eslint-disable-line no-param-reassign\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/es-set-tostringtag/index.js\n");

/***/ })

};
;