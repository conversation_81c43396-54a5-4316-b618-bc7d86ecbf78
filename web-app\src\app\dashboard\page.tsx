'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { apiHelpers } from '@/lib/api';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  MicrophoneIcon,
  PlusIcon,
  FolderIcon,
  UserGroupIcon,
  RocketLaunchIcon,
  CalendarIcon,
  LinkIcon,
  PlayIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';
import { useState } from 'react';
import { api } from '@/lib/api';

export default function DashboardPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Meeting Bot state
  const [meetingUrl, setMeetingUrl] = useState('');
  const [isSchedulingBot, setIsSchedulingBot] = useState(false);
  const [autoJoinEnabled, setAutoJoinEnabled] = useState(true);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  const { data: dashboardData, isLoading } = useQuery(
    'dashboard',
    apiHelpers.getDashboard,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  const { data: usageData } = useQuery(
    'usage',
    apiHelpers.getUsage,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  const { data: projectsData } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Bot sessions query
  const { data: botSessions, refetch: refetchBotSessions } = useQuery(
    'bot-sessions',
    async () => {
      const response = await api.get('/meeting-bot');
      return response.data.data;
    },
    {
      enabled: isAuthenticated,
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  // Quick bot scheduling function
  const handleQuickBotSchedule = async () => {
    if (!meetingUrl.trim()) {
      alert('Please enter a meeting URL');
      return;
    }

    setIsSchedulingBot(true);
    try {
      const response = await api.post('/meeting-bot/schedule', {
        meeting_url: meetingUrl,
        scheduled_time: new Date(Date.now() + 2 * 60 * 1000).toISOString(), // Start in 2 minutes
        platform: detectPlatform(meetingUrl),
        auto_start: true
      });

      if (response.data.success) {
        alert('Meeting bot scheduled successfully!');
        setMeetingUrl('');
        refetchBotSessions();
      }
    } catch (error) {
      console.error('Error scheduling bot:', error);
      alert('Failed to schedule meeting bot');
    } finally {
      setIsSchedulingBot(false);
    }
  };

  // Detect meeting platform from URL
  const detectPlatform = (url: string) => {
    if (url.includes('zoom.us')) return 'zoom';
    if (url.includes('meet.google.com')) return 'google_meet';
    if (url.includes('teams.microsoft.com')) return 'teams';
    return 'zoom'; // default
  };

  // Get active and upcoming bot sessions
  const activeBots = botSessions?.filter((bot: any) => bot.status === 'active') || [];
  const upcomingBots = botSessions?.filter((bot: any) => bot.status === 'scheduled') || [];

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const stats = [
    {
      name: 'Pending Tasks',
      value: dashboardData?.stats?.pending_tasks || 0,
      icon: ClockIcon,
      color: 'text-yellow-600 bg-yellow-100',
      href: '/tasks?filter=pending',
    },
    {
      name: 'Overdue Tasks',
      value: dashboardData?.stats?.overdue_tasks || 0,
      icon: ExclamationTriangleIcon,
      color: 'text-red-600 bg-red-100',
      href: '/tasks?filter=overdue',
    },
    {
      name: 'Total Meetings',
      value: dashboardData?.stats?.total_meetings || 0,
      icon: MicrophoneIcon,
      color: 'text-blue-600 bg-blue-100',
      href: '/meetings',
    },
    {
      name: 'Completed Tasks',
      value: dashboardData?.pendingActionItems?.filter((item: any) => item.status === 'completed').length || 0,
      icon: CheckCircleIcon,
      color: 'text-green-600 bg-green-100',
      href: '/tasks?filter=completed',
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="min-w-0 flex-1">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Dashboard
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Welcome back! Here's what's happening with your meetings and tasks.
            </p>
          </div>
          <div className="mt-4 flex md:ml-4 md:mt-0">
            <Link
              href="/meetings/upload"
              className="btn btn-primary"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Upload Meeting
            </Link>
          </div>
        </div>

        {/* Meeting Bot Widget */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg text-white">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <RocketLaunchIcon className="h-8 w-8 mr-3" />
                <div>
                  <h3 className="text-xl font-bold">Meeting Bot</h3>
                  <p className="text-blue-100">Join meetings automatically or on-demand</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {activeBots.length > 0 && (
                  <div className="flex items-center bg-green-500 bg-opacity-20 px-3 py-1 rounded-full">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    <span className="text-sm font-medium">{activeBots.length} Active</span>
                  </div>
                )}
                <Link
                  href="/meetings/bot/sessions"
                  className="text-blue-100 hover:text-white"
                >
                  <CogIcon className="h-5 w-5" />
                </Link>
              </div>
            </div>

            {/* Quick Bot Scheduler */}
            <div className="bg-white bg-opacity-10 rounded-lg p-4 mb-4">
              <h4 className="font-semibold mb-3 flex items-center">
                <LinkIcon className="h-4 w-4 mr-2" />
                Quick Bot Schedule
              </h4>
              <div className="flex space-x-3">
                <input
                  type="url"
                  value={meetingUrl}
                  onChange={(e) => setMeetingUrl(e.target.value)}
                  placeholder="Paste meeting URL (Zoom, Google Meet, Teams)"
                  className="flex-1 px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-blue-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                />
                <button
                  onClick={handleQuickBotSchedule}
                  disabled={isSchedulingBot || !meetingUrl.trim()}
                  className="bg-white text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSchedulingBot ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                      Scheduling...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-2" />
                      Join Now
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Bot Status & Calendar Integration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Active/Upcoming Bots */}
              <div className="bg-white bg-opacity-10 rounded-lg p-4">
                <h4 className="font-semibold mb-2 flex items-center">
                  <MicrophoneIcon className="h-4 w-4 mr-2" />
                  Bot Status
                </h4>
                {activeBots.length > 0 || upcomingBots.length > 0 ? (
                  <div className="space-y-2">
                    {activeBots.map((bot: any) => (
                      <div key={bot.id} className="flex items-center justify-between text-sm">
                        <span className="truncate">{bot.meeting_title || 'Active Meeting'}</span>
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                          <span>Live</span>
                        </div>
                      </div>
                    ))}
                    {upcomingBots.slice(0, 2).map((bot: any) => (
                      <div key={bot.id} className="flex items-center justify-between text-sm">
                        <span className="truncate">{bot.meeting_title || 'Scheduled Meeting'}</span>
                        <span className="text-blue-100">
                          {new Date(bot.scheduled_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-blue-100 text-sm">No active or scheduled bots</p>
                )}
              </div>

              {/* Calendar Integration */}
              <div className="bg-white bg-opacity-10 rounded-lg p-4">
                <h4 className="font-semibold mb-2 flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Auto-Join Settings
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Auto-join calendar meetings</span>
                    <button
                      onClick={() => setAutoJoinEnabled(!autoJoinEnabled)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        autoJoinEnabled ? 'bg-green-500' : 'bg-white bg-opacity-30'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          autoJoinEnabled ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                  <Link
                    href="/settings/calendar"
                    className="text-sm text-blue-100 hover:text-white underline"
                  >
                    Connect calendar →
                  </Link>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-3 mt-4">
              <Link
                href="/meetings/bot"
                className="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-opacity-30 transition-colors"
              >
                Schedule Advanced Bot
              </Link>
              <Link
                href="/meetings/bot/sessions"
                className="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-opacity-30 transition-colors"
              >
                View All Sessions
              </Link>
              <Link
                href="/meetings/upload"
                className="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-opacity-30 transition-colors"
              >
                Upload Recording
              </Link>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Link
              key={stat.name}
              href={stat.href}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`p-3 rounded-md ${stat.color}`}>
                      <stat.icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Usage Stats */}
        {usageData && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Usage This Month</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Meetings</span>
                    <span>{usageData.current.meetings} / {usageData.limits.meetings}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full"
                      style={{ width: `${Math.min(usageData.usage_percentage.meetings, 100)}%` }}
                    ></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Minutes</span>
                    <span>{usageData.current.minutes} / {usageData.limits.minutes}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full"
                      style={{ width: `${Math.min(usageData.usage_percentage.minutes, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              {usageData.subscription_tier === 'free' && (
                <div className="mt-4 p-4 bg-primary-50 rounded-md">
                  <p className="text-sm text-primary-700">
                    Upgrade to Pro for 300 minutes per month and advanced features.{' '}
                    <Link href="/settings/billing" className="font-medium underline">
                      Upgrade now
                    </Link>
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Recent Projects */}
        {projectsData && projectsData.length > 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Projects</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {projectsData.slice(0, 6).map((project: any) => (
                  <Link
                    key={project.id}
                    href={`/projects/${project.id}`}
                    className="block p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <FolderIcon className="h-6 w-6 text-primary-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {project.name}
                        </p>
                        <div className="flex items-center mt-1 text-xs text-gray-500">
                          <UserGroupIcon className="h-3 w-3 mr-1" />
                          {project.client_name}
                        </div>
                        <div className="mt-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            project.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : project.status === 'completed'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {project.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              <div className="mt-4 text-right">
                <Link href="/projects" className="text-sm font-medium text-primary-600 hover:text-primary-500">
                  View all projects →
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Recent Activity */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Recent Meetings */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Meetings</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {dashboardData?.recentMeetings?.length > 0 ? (
                dashboardData.recentMeetings.slice(0, 5).map((meeting: any) => (
                  <div key={meeting.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {meeting.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(meeting.created_at)} • {meeting.duration_minutes}m
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          meeting.transcription_status === 'completed' 
                            ? 'bg-green-100 text-green-800'
                            : meeting.transcription_status === 'processing'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {meeting.transcription_status}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-6 py-8 text-center">
                  <MicrophoneIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No meetings yet</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Upload your first meeting recording to get started.
                  </p>
                  <div className="mt-6">
                    <Link href="/meetings/upload" className="btn btn-primary">
                      Upload Meeting
                    </Link>
                  </div>
                </div>
              )}
            </div>
            {dashboardData?.recentMeetings?.length > 0 && (
              <div className="px-6 py-3 bg-gray-50 text-right">
                <Link href="/meetings" className="text-sm font-medium text-primary-600 hover:text-primary-500">
                  View all meetings →
                </Link>
              </div>
            )}
          </div>

          {/* Pending Tasks */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Pending Tasks</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {dashboardData?.pendingActionItems?.length > 0 ? (
                dashboardData.pendingActionItems.slice(0, 5).map((task: any) => (
                  <div key={task.id} className="px-6 py-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                          {task.task}
                        </p>
                        {task.deadline && (
                          <p className="text-sm text-gray-500">
                            Due: {formatDate(task.deadline)}
                          </p>
                        )}
                      </div>
                      <div className="flex-shrink-0 ml-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          task.priority === 'high' 
                            ? 'bg-red-100 text-red-800'
                            : task.priority === 'medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {task.priority}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-6 py-8 text-center">
                  <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No pending tasks</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    All caught up! Record a meeting to generate new action items.
                  </p>
                </div>
              )}
            </div>
            {dashboardData?.pendingActionItems?.length > 0 && (
              <div className="px-6 py-3 bg-gray-50 text-right">
                <Link href="/tasks" className="text-sm font-medium text-primary-600 hover:text-primary-500">
                  View all tasks →
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
