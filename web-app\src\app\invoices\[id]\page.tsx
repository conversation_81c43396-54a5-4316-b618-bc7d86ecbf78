'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Edit, Download, Send, Check, X } from 'lucide-react';
import { api } from '@/lib/api';

interface Invoice {
  id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string | null;
  sent_at: string | null;
  paid_at: string | null;
  notes: string | null;
  created_at: string;
  project: {
    id: string;
    name: string;
    client_name: string;
    client_email?: string;
  };
}

export default function InvoiceDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;
  
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    if (id) {
      fetchInvoice();
    }
  }, [id]);

  const fetchInvoice = async () => {
    try {
      const response = await api.get(`/invoices/${id}`);
      setInvoice(response.data.data);
    } catch (error) {
      console.error('Error fetching invoice:', error);
      router.push('/invoices');
    } finally {
      setLoading(false);
    }
  };

  const updateInvoiceStatus = async (status: string) => {
    if (!invoice) return;

    setUpdating(true);
    try {
      const response = await api.put(`/invoices/${invoice.id}`, { status });
      setInvoice(response.data.data);
    } catch (error) {
      console.error('Error updating invoice:', error);
      alert('Failed to update invoice status');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Invoice not found</h3>
        <Link href="/invoices" className="text-blue-600 hover:text-blue-800">
          ← Back to invoices
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link
            href="/invoices"
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Invoice {invoice.invoice_number}
            </h1>
            <p className="text-gray-600">{invoice.project.name}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
          </span>
          <Link
            href={`/invoices/${invoice.id}/edit`}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
          >
            <Edit className="h-4 w-4" />
            Edit
          </Link>
        </div>
      </div>

      {/* Invoice Details */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Invoice Details</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Bill To</h3>
              <div className="text-sm text-gray-900">
                <p className="font-medium">{invoice.project.client_name}</p>
                {invoice.project.client_email && (
                  <p className="text-gray-600">{invoice.project.client_email}</p>
                )}
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Invoice Info</h3>
              <div className="text-sm text-gray-900 space-y-1">
                <p><span className="text-gray-600">Invoice #:</span> {invoice.invoice_number}</p>
                <p><span className="text-gray-600">Created:</span> {new Date(invoice.created_at).toLocaleDateString()}</p>
                {invoice.due_date && (
                  <p><span className="text-gray-600">Due Date:</span> {new Date(invoice.due_date).toLocaleDateString()}</p>
                )}
                {invoice.sent_at && (
                  <p><span className="text-gray-600">Sent:</span> {new Date(invoice.sent_at).toLocaleDateString()}</p>
                )}
                {invoice.paid_at && (
                  <p><span className="text-gray-600">Paid:</span> {new Date(invoice.paid_at).toLocaleDateString()}</p>
                )}
              </div>
            </div>
          </div>

          {/* Amount */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <span className="text-lg font-medium text-gray-900">Total Amount</span>
              <span className="text-2xl font-bold text-gray-900">
                ${invoice.amount.toFixed(2)} {invoice.currency}
              </span>
            </div>
          </div>

          {/* Notes */}
          {invoice.notes && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Notes</h3>
              <p className="text-sm text-gray-900 whitespace-pre-wrap">{invoice.notes}</p>
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Actions</h2>
        <div className="flex flex-wrap gap-3">
          {invoice.status === 'draft' && (
            <button
              onClick={() => updateInvoiceStatus('sent')}
              disabled={updating}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
              {updating ? 'Sending...' : 'Send Invoice'}
            </button>
          )}
          
          {(invoice.status === 'sent' || invoice.status === 'overdue') && (
            <button
              onClick={() => updateInvoiceStatus('paid')}
              disabled={updating}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              {updating ? 'Updating...' : 'Mark as Paid'}
            </button>
          )}

          {invoice.status !== 'paid' && invoice.status !== 'cancelled' && (
            <button
              onClick={() => updateInvoiceStatus('cancelled')}
              disabled={updating}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              {updating ? 'Cancelling...' : 'Cancel Invoice'}
            </button>
          )}

          <button
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
            onClick={() => window.print()}
          >
            <Download className="h-4 w-4" />
            Download PDF
          </button>
        </div>
      </div>

      {/* Project Link */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">Related Project</h3>
        <Link
          href={`/projects/${invoice.project.id}`}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          {invoice.project.name} →
        </Link>
      </div>
    </div>
  );
}
