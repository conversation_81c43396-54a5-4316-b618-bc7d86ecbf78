import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { supabase } from '../config';
import { v4 as uuidv4 } from 'uuid';
import { scheduleJob, cancelJob } from 'node-schedule';

const router = Router();

// Schedule a bot to join a meeting
router.post('/schedule', authenticateToken, async (req, res) => {
  try {
    const { 
      meeting_url, 
      meeting_password, 
      scheduled_at, 
      platform, 
      title,
      project_id,
      bot_name = 'KaiNote Bot'
    } = req.body;

    const userId = req.user?.userId;

    // Validate required fields
    if (!meeting_url || !scheduled_at || !platform) {
      return res.status(400).json({
        success: false,
        message: 'Meeting URL, scheduled time, and platform are required'
      });
    }

    // Validate meeting URL format
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(meeting_url)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid meeting URL format'
      });
    }

    // Validate platform
    const supportedPlatforms = ['zoom', 'google-meet', 'microsoft-teams'];
    if (!supportedPlatforms.includes(platform)) {
      return res.status(400).json({
        success: false,
        message: 'Unsupported platform. Supported: zoom, google-meet, microsoft-teams'
      });
    }

    // Validate scheduled time (must be in future)
    const scheduledTime = new Date(scheduled_at);
    const now = new Date();
    if (scheduledTime <= now) {
      return res.status(400).json({
        success: false,
        message: 'Scheduled time must be in the future'
      });
    }

    // Create bot session record
    const botSessionId = uuidv4();
    const { data: botSession, error } = await supabase
      .from('bot_sessions')
      .insert({
        id: botSessionId,
        user_id: userId,
        project_id,
        meeting_url,
        meeting_password,
        scheduled_at: scheduledTime.toISOString(),
        platform,
        title: title || `Bot Meeting - ${platform}`,
        bot_name,
        status: 'scheduled',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating bot session:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to schedule bot session'
      });
    }

    // Schedule the bot to start 2 minutes before meeting time
    const botStartTime = new Date(scheduledTime.getTime() - 2 * 60 * 1000);
    
    scheduleJob(botSessionId, botStartTime, async () => {
      try {
        await startBotSession(botSessionId);
      } catch (error) {
        console.error('Error starting bot session:', error);
        await updateBotStatus(botSessionId, 'failed', 'Failed to start bot');
      }
    });

    res.json({
      success: true,
      data: {
        bot_session: botSession,
        start_time: botStartTime.toISOString()
      },
      message: 'Bot session scheduled successfully'
    });

  } catch (error) {
    console.error('Error scheduling bot:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get bot sessions for user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.userId;
    const { status, limit = 50 } = req.query;

    let query = supabase
      .from('bot_sessions')
      .select(`
        *,
        projects (
          name,
          client_name
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(parseInt(limit as string));

    if (status) {
      query = query.eq('status', status);
    }

    const { data: botSessions, error } = await query;

    if (error) {
      console.error('Error fetching bot sessions:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch bot sessions'
      });
    }

    res.json({
      success: true,
      data: botSessions
    });

  } catch (error) {
    console.error('Error fetching bot sessions:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get specific bot session
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;

    const { data: botSession, error } = await supabase
      .from('bot_sessions')
      .select(`
        *,
        projects (
          name,
          client_name
        ),
        meetings (
          id,
          title,
          transcript,
          action_items,
          summary
        )
      `)
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching bot session:', error);
      return res.status(404).json({
        success: false,
        message: 'Bot session not found'
      });
    }

    res.json({
      success: true,
      data: botSession
    });

  } catch (error) {
    console.error('Error fetching bot session:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Cancel a scheduled bot session
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;

    // Check if bot session exists and belongs to user
    const { data: botSession, error: fetchError } = await supabase
      .from('bot_sessions')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (fetchError || !botSession) {
      return res.status(404).json({
        success: false,
        message: 'Bot session not found'
      });
    }

    // Only allow cancellation of scheduled or running sessions
    if (!['scheduled', 'starting', 'running'].includes(botSession.status)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot cancel bot session in current status'
      });
    }

    // Cancel scheduled job
    cancelJob(id);

    // Update status to cancelled
    const { error: updateError } = await supabase
      .from('bot_sessions')
      .update({
        status: 'cancelled',
        ended_at: new Date().toISOString(),
        error_message: 'Cancelled by user'
      })
      .eq('id', id);

    if (updateError) {
      console.error('Error cancelling bot session:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to cancel bot session'
      });
    }

    res.json({
      success: true,
      message: 'Bot session cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling bot session:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Get bot session status and logs
router.get('/:id/status', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;

    const { data: botSession, error } = await supabase
      .from('bot_sessions')
      .select('id, status, error_message, started_at, ended_at, transcript_progress')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        message: 'Bot session not found'
      });
    }

    // Get recent logs
    const { data: logs } = await supabase
      .from('bot_logs')
      .select('*')
      .eq('bot_session_id', id)
      .order('created_at', { ascending: false })
      .limit(50);

    res.json({
      success: true,
      data: {
        ...botSession,
        logs: logs || []
      }
    });

  } catch (error) {
    console.error('Error fetching bot status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Helper functions
async function startBotSession(botSessionId: string) {
  // Update status to starting
  await updateBotStatus(botSessionId, 'starting', 'Initializing bot...');
  
  // Here you would implement the actual bot starting logic
  // This would involve:
  // 1. Starting a Docker container with Puppeteer
  // 2. Setting up audio capture
  // 3. Joining the meeting
  // 4. Starting transcription
  
  console.log(`Starting bot session: ${botSessionId}`);
  
  // For now, simulate the process
  setTimeout(async () => {
    await updateBotStatus(botSessionId, 'running', 'Bot joined meeting successfully');
  }, 5000);
}

async function updateBotStatus(botSessionId: string, status: string, message?: string) {
  const updates: any = { status };
  
  if (status === 'running' && !message) {
    updates.started_at = new Date().toISOString();
  } else if (['completed', 'failed', 'cancelled'].includes(status)) {
    updates.ended_at = new Date().toISOString();
  }
  
  if (message) {
    updates.error_message = message;
  }

  await supabase
    .from('bot_sessions')
    .update(updates)
    .eq('id', botSessionId);

  // Log the status change
  await supabase
    .from('bot_logs')
    .insert({
      bot_session_id: botSessionId,
      level: status === 'failed' ? 'error' : 'info',
      message: message || `Status changed to ${status}`,
      created_at: new Date().toISOString()
    });
}

export default router;
