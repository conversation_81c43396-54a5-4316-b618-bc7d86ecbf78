'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  ChatBubbleLeftRightIcon,
  DocumentDuplicateIcon,
  ShareIcon,
  PlusIcon,
  CheckIcon,
  XMarkIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { apiHelpers } from '@/lib/api';
import toast from 'react-hot-toast';

interface Project {
  id: string;
  name: string;
  client_name: string;
  client_email?: string;
  description?: string;
  status: string;
  budget?: number;
  deadline?: string;
  created_at: string;
  updated_at: string;
}

interface Meeting {
  id: string;
  title: string;
  platform: string;
  duration_minutes: number;
  recorded_at: string;
  transcription_status: string;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  due_date?: string;
  assigned_to?: string;
  created_at: string;
}

interface Document {
  id: string;
  name: string;
  file_type: string;
  file_size: number;
  uploaded_at: string;
  uploaded_by: string;
}

interface Invoice {
  id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string;
  sent_at?: string;
  paid_at?: string;
  notes?: string;
  created_at: string;
}

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<Project | null>(null);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [showNewTaskForm, setShowNewTaskForm] = useState(false);
  const [showNewInvoiceForm, setShowNewInvoiceForm] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [showNewMeetingForm, setShowNewMeetingForm] = useState(false);
  const [showDocumentUpload, setShowDocumentUpload] = useState(false);

  useEffect(() => {
    if (projectId) {
      fetchProjectData();
    }
  }, [projectId]);

  const fetchProjectData = async () => {
    try {
      setIsLoading(true);

      // Fetch project details
      const projectResponse = await apiHelpers.getProject(projectId);
      if (projectResponse.data.success) {
        setProject(projectResponse.data.data);
      }

      // Fetch project meetings
      const meetingsResponse = await apiHelpers.getProjectMeetings(projectId);
      if (meetingsResponse.data.success) {
        setMeetings(meetingsResponse.data.data);
      }

      // Load demo data for other tabs
      setTasks([
        {
          id: '1',
          title: 'Design wireframes',
          description: 'Create initial wireframes for the project',
          status: 'completed',
          priority: 'high',
          due_date: '2025-06-25',
          assigned_to: 'Designer',
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          title: 'Implement authentication',
          description: 'Set up user login and registration',
          status: 'in_progress',
          priority: 'high',
          due_date: '2025-06-30',
          assigned_to: 'Developer',
          created_at: new Date().toISOString()
        },
        {
          id: '3',
          title: 'Content review',
          description: 'Review and approve website content',
          status: 'pending',
          priority: 'medium',
          due_date: '2025-07-05',
          assigned_to: 'Client',
          created_at: new Date().toISOString()
        }
      ]);

      setDocuments([
        {
          id: '1',
          name: 'Project Requirements.pdf',
          file_type: 'pdf',
          file_size: 2048000,
          uploaded_at: new Date().toISOString(),
          uploaded_by: 'Project Manager'
        },
        {
          id: '2',
          name: 'Design Mockups.figma',
          file_type: 'figma',
          file_size: 5120000,
          uploaded_at: new Date().toISOString(),
          uploaded_by: 'Designer'
        }
      ]);

      setInvoices([
        {
          id: '1',
          invoice_number: 'INV-001',
          amount: 5000,
          currency: 'USD',
          status: 'sent',
          due_date: '2025-07-15',
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          invoice_number: 'INV-002',
          amount: 3000,
          currency: 'USD',
          status: 'draft',
          due_date: '2025-08-15',
          created_at: new Date().toISOString()
        }
      ]);

    } catch (error) {
      console.error('Error fetching project data:', error);
      toast.error('Failed to load project data');
    } finally {
      setIsLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: DocumentTextIcon },
    { id: 'meetings', name: 'Meetings', icon: ChatBubbleLeftRightIcon },
    { id: 'tasks', name: 'Tasks', icon: ClipboardDocumentListIcon },
    { id: 'documents', name: 'Documents', icon: DocumentDuplicateIcon },
    { id: 'invoices', name: 'Invoices', icon: CurrencyDollarIcon },
    { id: 'client', name: 'Client Portal', icon: UserGroupIcon },
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              <div className="lg:col-span-1">
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
              <div className="lg:col-span-3">
                <div className="h-96 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Project not found</h1>
            <Link href="/projects" className="text-primary-600 hover:text-primary-500 mt-4 inline-block">
              ← Back to Projects
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link
                  href="/projects"
                  className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-1" />
                  Back to Projects
                </Link>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setActiveTab('client')}
                  className="btn btn-secondary"
                >
                  <ShareIcon className="h-4 w-4 mr-2" />
                  Share with Client
                </button>
                <button
                  onClick={() => setShowNewMeetingForm(true)}
                  className="btn btn-primary"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  New Meeting
                </button>
              </div>
            </div>
            
            <div className="mt-4">
              <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <UserGroupIcon className="h-4 w-4 mr-1" />
                  {project.client_name}
                </span>
                {project.deadline && (
                  <span className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    Due {new Date(project.deadline).toLocaleDateString()}
                  </span>
                )}
                {project.budget && (
                  <span className="flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                    ${project.budget.toLocaleString()}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white shadow rounded-lg">
              {activeTab === 'overview' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Overview</h2>
                  
                  {/* Project Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{meetings.length}</div>
                      <div className="text-sm text-blue-600">Total Meetings</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">0</div>
                      <div className="text-sm text-green-600">Completed Tasks</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">0</div>
                      <div className="text-sm text-yellow-600">Pending Tasks</div>
                    </div>
                  </div>

                  {/* Project Description */}
                  {project.description && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
                      <p className="text-gray-600">{project.description}</p>
                    </div>
                  )}

                  {/* Recent Activity */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                    <div className="space-y-3">
                      {meetings.slice(0, 3).map((meeting) => (
                        <Link
                          key={meeting.id}
                          href={`/meetings/${meeting.id}`}
                          className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                        >
                          <ChatBubbleLeftRightIcon className="h-5 w-5 text-gray-400" />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">{meeting.title}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(meeting.recorded_at).toLocaleDateString()} • {meeting.duration_minutes}m
                            </p>
                          </div>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            meeting.transcription_status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {meeting.transcription_status}
                          </span>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'meetings' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Meetings</h2>
                    <button
                      onClick={() => setShowNewMeetingForm(true)}
                      className="btn btn-primary"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Schedule Meeting
                    </button>
                  </div>
                  
                  {meetings.length === 0 ? (
                    <div className="text-center py-12">
                      <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No meetings yet</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Schedule your first meeting to get started.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {meetings.map((meeting) => (
                        <div key={meeting.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="text-lg font-medium text-gray-900">{meeting.title}</h3>
                              <p className="text-sm text-gray-500">
                                {new Date(meeting.recorded_at).toLocaleDateString()} • {meeting.duration_minutes} minutes
                              </p>
                            </div>
                            <div className="flex items-center space-x-3">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                meeting.transcription_status === 'completed' 
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {meeting.transcription_status}
                              </span>
                              <Link 
                                href={`/meetings/${meeting.id}`}
                                className="text-primary-600 hover:text-primary-500 text-sm font-medium"
                              >
                                View Details →
                              </Link>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Tasks Tab */}
              {activeTab === 'tasks' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Tasks</h2>
                    <button
                      onClick={() => setShowNewTaskForm(true)}
                      className="btn btn-primary"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Add Task
                    </button>
                  </div>

                  {/* Task Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{tasks.length}</div>
                      <div className="text-sm text-blue-600">Total Tasks</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {tasks.filter(t => t.status === 'completed').length}
                      </div>
                      <div className="text-sm text-green-600">Completed</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        {tasks.filter(t => t.status === 'in_progress').length}
                      </div>
                      <div className="text-sm text-yellow-600">In Progress</div>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">
                        {tasks.filter(t => t.status === 'pending').length}
                      </div>
                      <div className="text-sm text-red-600">Pending</div>
                    </div>
                  </div>

                  {/* Tasks List */}
                  <div className="space-y-4">
                    {tasks.map((task) => (
                      <div key={task.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <button
                              onClick={() => {
                                const newStatus = task.status === 'completed' ? 'pending' : 'completed';
                                setTasks(tasks.map(t => t.id === task.id ? {...t, status: newStatus} : t));
                                toast.success(`Task marked as ${newStatus === 'completed' ? 'complete' : 'incomplete'}`);
                              }}
                              className={`p-2 rounded-full transition-colors ${
                                task.status === 'completed'
                                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                                  : 'bg-gray-100 text-gray-400 hover:bg-green-100 hover:text-green-600'
                              }`}
                              title={task.status === 'completed' ? 'Mark as incomplete' : 'Mark as complete'}
                            >
                              <CheckIcon className="h-4 w-4" />
                            </button>
                            <div>
                              <h3 className={`text-lg font-medium ${
                                task.status === 'completed' ? 'text-gray-500 line-through' : 'text-gray-900'
                              }`}>
                                {task.title}
                              </h3>
                              {task.description && (
                                <p className="text-sm text-gray-500">{task.description}</p>
                              )}
                              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span className={`px-2 py-1 rounded-full ${
                                  task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                  task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-green-100 text-green-800'
                                }`}>
                                  {task.priority} priority
                                </span>
                                <span className={`px-2 py-1 rounded-full ${
                                  task.status === 'completed' ? 'bg-green-100 text-green-800' :
                                  task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {task.status.replace('_', ' ')}
                                </span>
                                {task.due_date && (
                                  <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                                )}
                                {task.assigned_to && (
                                  <span>Assigned to: {task.assigned_to}</span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => setEditingTask(task)}
                              className="p-2 text-gray-400 hover:text-blue-600"
                              title="Edit task"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => {
                                if (confirm('Are you sure you want to delete this task?')) {
                                  setTasks(tasks.filter(t => t.id !== task.id));
                                  toast.success('Task deleted');
                                }
                              }}
                              className="p-2 text-gray-400 hover:text-red-600"
                              title="Delete task"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* New Task Form */}
                  {showNewTaskForm && (
                    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
                      <div className="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Task</h3>
                        <form onSubmit={(e) => {
                          e.preventDefault();
                          const formData = new FormData(e.target as HTMLFormElement);
                          const newTask: Task = {
                            id: Date.now().toString(),
                            title: formData.get('title') as string,
                            description: formData.get('description') as string,
                            status: 'pending',
                            priority: formData.get('priority') as 'low' | 'medium' | 'high',
                            due_date: formData.get('due_date') as string,
                            assigned_to: formData.get('assigned_to') as string,
                            created_at: new Date().toISOString()
                          };
                          setTasks([...tasks, newTask]);
                          setShowNewTaskForm(false);
                          toast.success('Task added successfully');
                        }}>
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Title</label>
                              <input
                                type="text"
                                name="title"
                                required
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Description</label>
                              <textarea
                                name="description"
                                rows={3}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Priority</label>
                              <select
                                name="priority"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Due Date</label>
                              <input
                                type="date"
                                name="due_date"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Assigned To</label>
                              <input
                                type="text"
                                name="assigned_to"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                          </div>
                          <div className="flex justify-end space-x-3 mt-6">
                            <button
                              type="button"
                              onClick={() => setShowNewTaskForm(false)}
                              className="btn btn-secondary"
                            >
                              Cancel
                            </button>
                            <button type="submit" className="btn btn-primary">
                              Add Task
                            </button>
                          </div>
                        </form>
                      </div>
                    </div>
                  )}

                  {/* Edit Task Form */}
                  {editingTask && (
                    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
                      <div className="bg-white rounded-lg p-6 w-full max-w-md">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Task</h3>
                        <form onSubmit={(e) => {
                          e.preventDefault();
                          const formData = new FormData(e.target as HTMLFormElement);
                          const updatedTask: Task = {
                            ...editingTask,
                            title: formData.get('title') as string,
                            description: formData.get('description') as string,
                            priority: formData.get('priority') as 'low' | 'medium' | 'high',
                            due_date: formData.get('due_date') as string,
                            assigned_to: formData.get('assigned_to') as string,
                          };
                          setTasks(tasks.map(t => t.id === editingTask.id ? updatedTask : t));
                          setEditingTask(null);
                          toast.success('Task updated successfully');
                        }}>
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Title</label>
                              <input
                                type="text"
                                name="title"
                                defaultValue={editingTask.title}
                                required
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Description</label>
                              <textarea
                                name="description"
                                defaultValue={editingTask.description}
                                rows={3}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Priority</label>
                              <select
                                name="priority"
                                defaultValue={editingTask.priority}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Due Date</label>
                              <input
                                type="date"
                                name="due_date"
                                defaultValue={editingTask.due_date}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Assigned To</label>
                              <input
                                type="text"
                                name="assigned_to"
                                defaultValue={editingTask.assigned_to}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                          </div>
                          <div className="flex justify-end space-x-3 mt-6">
                            <button
                              type="button"
                              onClick={() => setEditingTask(null)}
                              className="btn btn-secondary"
                            >
                              Cancel
                            </button>
                            <button type="submit" className="btn btn-primary">
                              Update Task
                            </button>
                          </div>
                        </form>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Documents Tab */}
              {activeTab === 'documents' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Documents</h2>
                    <button
                      onClick={() => setShowDocumentUpload(true)}
                      className="btn btn-primary"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Upload Document
                    </button>
                  </div>

                  {documents.length === 0 ? (
                    <div className="text-center py-12">
                      <DocumentDuplicateIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No documents yet</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Upload your first document to get started.
                      </p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {documents.map((doc) => (
                        <div key={doc.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                <DocumentDuplicateIcon className="h-8 w-8 text-gray-400" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="text-sm font-medium text-gray-900 truncate">
                                  {doc.name}
                                </h3>
                                <p className="text-xs text-gray-500">
                                  {(doc.file_size / 1024 / 1024).toFixed(2)} MB • {doc.file_type.toUpperCase()}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <button
                                className="p-1 text-gray-400 hover:text-blue-600"
                                title="View document"
                                onClick={() => toast.info('Document viewer coming soon')}
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => {
                                  if (confirm('Are you sure you want to delete this document?')) {
                                    setDocuments(documents.filter(d => d.id !== doc.id));
                                    toast.success('Document deleted');
                                  }
                                }}
                                className="p-1 text-gray-400 hover:text-red-600"
                                title="Delete document"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500">
                            <p>Uploaded by {doc.uploaded_by}</p>
                            <p>{new Date(doc.uploaded_at).toLocaleDateString()}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Invoices Tab */}
              {activeTab === 'invoices' && (
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">Project Invoices</h2>
                    <button
                      onClick={() => setShowNewInvoiceForm(true)}
                      className="btn btn-primary"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Create Invoice
                    </button>
                  </div>

                  {/* Invoice Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{invoices.length}</div>
                      <div className="text-sm text-blue-600">Total Invoices</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        ${invoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + i.amount, 0).toLocaleString()}
                      </div>
                      <div className="text-sm text-green-600">Paid Amount</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        ${invoices.filter(i => i.status === 'sent').reduce((sum, i) => sum + i.amount, 0).toLocaleString()}
                      </div>
                      <div className="text-sm text-yellow-600">Outstanding</div>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">
                        {invoices.filter(i => i.status === 'overdue').length}
                      </div>
                      <div className="text-sm text-red-600">Overdue</div>
                    </div>
                  </div>

                  {/* Invoices List */}
                  <div className="space-y-4">
                    {invoices.map((invoice) => (
                      <div key={invoice.id} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="text-lg font-medium text-gray-900">
                                {invoice.invoice_number}
                              </h3>
                              <span className={`px-3 py-1 text-xs font-semibold rounded-full ${
                                invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                                invoice.status === 'sent' ? 'bg-blue-100 text-blue-800' :
                                invoice.status === 'overdue' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                              </span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                              <div>
                                <span className="font-medium">Created:</span> {new Date(invoice.created_at).toLocaleDateString()}
                              </div>
                              <div>
                                <span className="font-medium">Due:</span> {new Date(invoice.due_date).toLocaleDateString()}
                              </div>
                              <div>
                                <span className="font-medium">Project:</span> {project?.name}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center space-x-6">
                            <div className="text-right">
                              <div className="text-2xl font-bold text-gray-900">
                                ${invoice.amount.toLocaleString()}
                              </div>
                              <div className="text-sm text-gray-500">{invoice.currency}</div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Link
                                href={`/invoices/${invoice.id}`}
                                className="p-2 text-gray-400 hover:text-blue-600"
                                title="View invoice"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Link>
                              <Link
                                href={`/invoices/${invoice.id}/edit`}
                                className="p-2 text-gray-400 hover:text-gray-600"
                                title="Edit invoice"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>

                              {/* Status Actions */}
                              {invoice.status === 'draft' && (
                                <button
                                  onClick={() => {
                                    setInvoices(invoices.map(i =>
                                      i.id === invoice.id
                                        ? {...i, status: 'sent', sent_at: new Date().toISOString()}
                                        : i
                                    ));
                                    toast.success('Invoice marked as sent');
                                  }}
                                  className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200"
                                  title="Mark as sent"
                                >
                                  Send
                                </button>
                              )}

                              {invoice.status === 'sent' && (
                                <button
                                  onClick={() => {
                                    setInvoices(invoices.map(i =>
                                      i.id === invoice.id
                                        ? {...i, status: 'paid', paid_at: new Date().toISOString()}
                                        : i
                                    ));
                                    toast.success('Invoice marked as paid');
                                  }}
                                  className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200"
                                  title="Mark as paid"
                                >
                                  Mark Paid
                                </button>
                              )}

                              <button
                                onClick={() => {
                                  if (confirm('Are you sure you want to delete this invoice?')) {
                                    setInvoices(invoices.filter(i => i.id !== invoice.id));
                                    toast.success('Invoice deleted');
                                  }
                                }}
                                className="p-2 text-gray-400 hover:text-red-600"
                                title="Delete invoice"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* New Invoice Form */}
                  {showNewInvoiceForm && (
                    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
                      <div className="bg-white rounded-lg p-6 w-full max-w-lg">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Invoice</h3>
                        <form onSubmit={(e) => {
                          e.preventDefault();
                          const formData = new FormData(e.target as HTMLFormElement);
                          const newInvoice: Invoice = {
                            id: Date.now().toString(),
                            invoice_number: `INV-${String(invoices.length + 1).padStart(3, '0')}`,
                            amount: parseFloat(formData.get('amount') as string),
                            currency: formData.get('currency') as string,
                            status: 'draft',
                            due_date: formData.get('due_date') as string,
                            created_at: new Date().toISOString()
                          };
                          setInvoices([...invoices, newInvoice]);
                          setShowNewInvoiceForm(false);
                          toast.success('Invoice created successfully');
                        }}>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
                              <input
                                type="text"
                                name="invoice_number"
                                defaultValue={`INV-${String(invoices.length + 1).padStart(3, '0')}`}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Amount</label>
                              <input
                                type="number"
                                name="amount"
                                step="0.01"
                                required
                                placeholder="0.00"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Currency</label>
                              <select
                                name="currency"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              >
                                <option value="USD">USD - US Dollar</option>
                                <option value="EUR">EUR - Euro</option>
                                <option value="GBP">GBP - British Pound</option>
                                <option value="CAD">CAD - Canadian Dollar</option>
                                <option value="AUD">AUD - Australian Dollar</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">Due Date</label>
                              <input
                                type="date"
                                name="due_date"
                                required
                                min={new Date().toISOString().split('T')[0]}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                              />
                            </div>
                          </div>

                          <div className="mt-4">
                            <label className="block text-sm font-medium text-gray-700">Notes (Optional)</label>
                            <textarea
                              name="notes"
                              rows={3}
                              placeholder="Add any additional notes or payment terms..."
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            />
                          </div>

                          <div className="flex justify-between items-center mt-6">
                            <div className="text-sm text-gray-500">
                              Invoice will be created for: <strong>{project?.client_name}</strong>
                            </div>
                            <div className="flex space-x-3">
                              <button
                                type="button"
                                onClick={() => setShowNewInvoiceForm(false)}
                                className="btn btn-secondary"
                              >
                                Cancel
                              </button>
                              <button type="submit" className="btn btn-primary">
                                Create Invoice
                              </button>
                            </div>
                          </div>
                        </form>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Client Portal Tab */}
              {activeTab === 'client' && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-6">Client Portal</h2>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-8 w-8 text-blue-600 mr-3" />
                      <div>
                        <h3 className="text-lg font-medium text-blue-900">Share Project with Client</h3>
                        <p className="text-sm text-blue-700">
                          Give your client access to view project progress, meetings, and documents.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Client Access Settings</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Meetings</h4>
                            <p className="text-sm text-gray-500">Allow client to view meeting summaries and recordings</p>
                          </div>
                          <input type="checkbox" defaultChecked className="h-4 w-4 text-primary-600" />
                        </div>
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Documents</h4>
                            <p className="text-sm text-gray-500">Allow client to download project documents</p>
                          </div>
                          <input type="checkbox" defaultChecked className="h-4 w-4 text-primary-600" />
                        </div>
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Tasks</h4>
                            <p className="text-sm text-gray-500">Allow client to see task progress</p>
                          </div>
                          <input type="checkbox" defaultChecked className="h-4 w-4 text-primary-600" />
                        </div>
                        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                          <div>
                            <h4 className="font-medium text-gray-900">View Invoices</h4>
                            <p className="text-sm text-gray-500">Allow client to view and pay invoices</p>
                          </div>
                          <input type="checkbox" className="h-4 w-4 text-primary-600" />
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Generate Client Link</h3>
                      <form onSubmit={(e) => {
                        e.preventDefault();
                        const formData = new FormData(e.target as HTMLFormElement);
                        const email = formData.get('email') as string;
                        if (email) {
                          toast.success(`Invitation sent to ${email}`);
                          (e.target as HTMLFormElement).reset();
                        }
                      }}>
                        <div className="flex space-x-3">
                          <input
                            type="email"
                            name="email"
                            placeholder="<EMAIL>"
                            required
                            className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                          <button type="submit" className="btn btn-primary">
                            Send Invitation
                          </button>
                        </div>
                      </form>
                      <p className="text-sm text-gray-500 mt-2">
                        An email invitation will be sent to the client with secure access to this project.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Fallback for any remaining tabs */}
              {!['overview', 'meetings', 'tasks', 'documents', 'invoices', 'client'].includes(activeTab) && (
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    {tabs.find(t => t.id === activeTab)?.name}
                  </h2>
                  <div className="text-center py-12">
                    <div className="text-gray-400">
                      {tabs.find(t => t.id === activeTab)?.name} feature coming soon...
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* New Meeting Form */}
      {showNewMeetingForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule New Meeting</h3>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const newMeeting: Meeting = {
                id: Date.now().toString(),
                title: formData.get('title') as string,
                platform: formData.get('platform') as string,
                duration_minutes: 0,
                recorded_at: formData.get('scheduled_at') as string,
                transcription_status: 'scheduled'
              };
              setMeetings([...meetings, newMeeting]);
              setShowNewMeetingForm(false);
              toast.success('Meeting scheduled successfully');
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Meeting Title</label>
                  <input
                    type="text"
                    name="title"
                    required
                    placeholder="e.g., Weekly Project Review"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Platform</label>
                  <select
                    name="platform"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="zoom">Zoom</option>
                    <option value="teams">Microsoft Teams</option>
                    <option value="meet">Google Meet</option>
                    <option value="webex">Cisco Webex</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Scheduled Date & Time</label>
                  <input
                    type="datetime-local"
                    name="scheduled_at"
                    required
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowNewMeetingForm(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Schedule Meeting
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Document Upload Form */}
      {showDocumentUpload && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Document</h3>
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target as HTMLFormElement);
              const file = formData.get('file') as File;

              if (file) {
                const newDocument: Document = {
                  id: Date.now().toString(),
                  name: file.name,
                  file_type: file.name.split('.').pop() || 'unknown',
                  file_size: file.size,
                  uploaded_at: new Date().toISOString(),
                  uploaded_by: 'Current User'
                };
                setDocuments([...documents, newDocument]);
                setShowDocumentUpload(false);
                toast.success('Document uploaded successfully');
              }
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Select File</label>
                  <input
                    type="file"
                    name="file"
                    required
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                    className="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Supported formats: PDF, Word, Excel, PowerPoint, Images, Text files
                  </p>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowDocumentUpload(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Upload Document
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
