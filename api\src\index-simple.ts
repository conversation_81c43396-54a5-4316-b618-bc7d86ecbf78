import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Simple auth middleware for demo
const demoAuth = (req: any, res: any, next: any) => {
  req.user = {
    id: 'demo-user-id',
    userId: 'demo-user-id',
    email: '<EMAIL>',
    name: 'Demo User',
    subscription_tier: 'pro'
  };
  next();
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'KaiNote API is running!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Demo authentication endpoints
app.post('/api/auth/signin', (req, res) => {
  const { email, password } = req.body;

  // Demo authentication - accept any email/password
  const demoUser = {
    id: 'demo-user-id',
    email: email || '<EMAIL>',
    name: 'Demo User',
    subscription_tier: 'pro'
  };

  const demoToken = 'demo-token-' + Date.now();

  res.json({
    success: true,
    data: {
      user: demoUser,
      token: demoToken
    }
  });
});

app.post('/api/auth/signup', (req, res) => {
  const { email, password, name } = req.body;

  // Demo signup - accept any details
  const demoUser = {
    id: 'demo-user-id',
    email: email || '<EMAIL>',
    name: name || 'Demo User',
    subscription_tier: 'pro'
  };

  const demoToken = 'demo-token-' + Date.now();

  res.json({
    success: true,
    data: {
      user: demoUser,
      token: demoToken
    }
  });
});

app.post('/api/auth/verify', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: 'No token provided'
    });
  }

  // Demo verification - accept any token that starts with 'demo-token'
  const token = authHeader.substring(7);
  if (token.startsWith('demo-token')) {
    const demoUser = {
      id: 'demo-user-id',
      email: '<EMAIL>',
      name: 'Demo User',
      subscription_tier: 'pro'
    };

    res.json({
      success: true,
      data: {
        user: demoUser
      }
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
});

// Demo API endpoints
app.get('/api/projects', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Demo Project 1',
        description: 'A sample project for demonstration',
        client_name: 'Demo Client',
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Demo Project 2',
        description: 'Another sample project',
        client_name: 'Another Client',
        status: 'active',
        created_at: new Date().toISOString()
      }
    ]
  });
});

app.get('/api/meetings', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'Demo Meeting 1',
        project_id: '1',
        recorded_at: new Date().toISOString(),
        duration_minutes: 30,
        status: 'completed'
      }
    ]
  });
});

app.get('/api/time-tracking/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalHours: 40,
        billableHours: 35,
        totalRevenue: 2625,
        averageHourlyRate: 75
      }
    }
  });
});

app.get('/api/expenses/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalAmount: 1250,
        taxDeductibleAmount: 1000,
        billableAmount: 500,
        averageExpense: 125
      }
    }
  });
});

app.get('/api/clients/analytics', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      totalClients: 5,
      statusBreakdown: {
        active: 3,
        prospect: 2
      },
      recentCommunications: []
    }
  });
});

app.get('/api/financial/dashboard', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalRevenue: 5000,
        totalExpenses: 1250,
        grossProfit: 3750,
        profitMargin: 75,
        outstandingAmount: 2000,
        taxDeductibleExpenses: 1000
      },
      breakdown: {
        invoiceRevenue: 3000,
        timeRevenue: 2000,
        revenueByClient: [],
        expensesByCategory: []
      },
      trends: {
        monthly: []
      },
      outstanding: {
        invoices: 2,
        amount: 2000
      }
    }
  });
});

app.get('/api/automation/dashboard', demoAuth, (req, res) => {
  res.json({
    success: true,
    data: {
      summary: {
        totalRules: 3,
        activeRules: 2,
        activeWorkflows: 1,
        upcomingTasks: 2,
        pendingFollowups: 1
      },
      activeWorkflows: [],
      upcomingTasks: [],
      pendingFollowups: [],
      recentExecutions: []
    }
  });
});

// Catch all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use((error: any, req: any, res: any, next: any) => {
  console.error('API Error:', error);
  
  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 KaiNote API Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
});

export default app;
