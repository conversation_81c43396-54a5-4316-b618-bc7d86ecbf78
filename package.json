{"name": "kainote-freelancers", "version": "1.0.0", "description": "Transform virtual client meetings into actionable results for freelancers", "private": true, "workspaces": ["chrome-extension", "web-app", "api", "shared"], "scripts": {"dev": "concurrently \"npm run dev:web\" \"npm run dev:api\"", "dev:web": "cd web-app && npm run dev", "dev:api": "cd api && npm run dev", "build": "npm run build:shared && npm run build:web && npm run build:api && npm run build:extension", "build:shared": "cd shared && npm run build", "build:web": "cd web-app && npm run build", "build:api": "cd api && npm run build", "build:extension": "cd chrome-extension && npm run build", "test": "npm run test:web && npm run test:api", "test:web": "cd web-app && npm test", "test:api": "cd api && npm test", "lint": "npm run lint:web && npm run lint:api && npm run lint:extension", "lint:web": "cd web-app && npm run lint", "lint:api": "cd api && npm run lint", "lint:extension": "cd chrome-extension && npm run lint", "clean": "rm -rf node_modules */node_modules */dist */.next"}, "devDependencies": {"concurrently": "^8.2.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "author": "KaiNote Team", "license": "MIT", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}