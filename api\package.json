{"name": "kainote-api", "version": "1.0.0", "description": "KaiNote API backend for meeting transcription and action item extraction", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "clean": "rm -rf dist"}, "dependencies": {"@kainote/shared": "workspace:*", "@supabase/supabase-js": "^2.38.5", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "openai": "^4.24.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "form-data": "^4.0.0", "axios": "^1.6.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/joi": "^17.2.3", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/fluent-ffmpeg": "^2.1.24", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}