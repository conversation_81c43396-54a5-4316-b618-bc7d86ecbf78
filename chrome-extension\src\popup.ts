// Popup script for KaiNote Chrome Extension

interface PopupState {
  isAuthenticated: boolean;
  user: any;
  isRecording: boolean;
  currentTab: any;
  usageStats: any;
}

let popupState: PopupState = {
  isAuthenticated: false,
  user: null,
  isRecording: false,
  currentTab: null,
  usageStats: null
};

let recordingTimer: number | null = null;
let recordingStartTime: number = 0;

// DOM elements
const elements = {
  loadingSection: document.getElementById('loadingSection')!,
  authSection: document.getElementById('authSection')!,
  mainContent: document.getElementById('mainContent')!,
  errorSection: document.getElementById('errorSection')!,
  
  // Header
  statusIndicator: document.getElementById('statusIndicator')!,
  statusText: document.getElementById('statusText')!,
  
  // Auth
  signInBtn: document.getElementById('signInBtn')!,
  signUpBtn: document.getElementById('signUpBtn')!,
  
  // Meeting info
  meetingPlatform: document.getElementById('meetingPlatform')!,
  platformIcon: document.getElementById('platformIcon')!,
  platformText: document.getElementById('platformText')!,
  meetingTitle: document.getElementById('meetingTitle')!,
  
  // Recording controls
  statusIdle: document.getElementById('statusIdle')!,
  statusRecording: document.getElementById('statusRecording')!,
  recordingTime: document.getElementById('recordingTime')!,
  recordBtn: document.getElementById('recordBtn')!,
  stopBtn: document.getElementById('stopBtn')!,
  
  // Usage stats
  meetingsCount: document.getElementById('meetingsCount')!,
  minutesUsed: document.getElementById('minutesUsed')!,
  usageProgress: document.getElementById('usageProgress')!,
  usageText: document.getElementById('usageText')!,
  
  // Quick actions
  dashboardBtn: document.getElementById('dashboardBtn')!,
  tasksBtn: document.getElementById('tasksBtn')!,
  settingsBtn: document.getElementById('settingsBtn')!,
  
  // Error
  errorMessage: document.getElementById('errorMessage')!,
  retryBtn: document.getElementById('retryBtn')!
};

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  console.log('KaiNote popup loaded');
  
  try {
    await initializePopup();
  } catch (error) {
    console.error('Error initializing popup:', error);
    showError('Failed to initialize KaiNote');
  }
});

async function initializePopup(): Promise<void> {
  showLoading();
  
  // Get current tab
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
  popupState.currentTab = tabs[0];
  
  // Check authentication status
  await checkAuthStatus();
  
  // Check recording status
  await checkRecordingStatus();
  
  // Load usage stats if authenticated
  if (popupState.isAuthenticated) {
    await loadUsageStats();
  }
  
  // Update UI
  updateUI();
  
  // Set up event listeners
  setupEventListeners();
}

async function checkAuthStatus(): Promise<void> {
  try {
    const response = await sendMessageToBackground({
      type: 'GET_USER_STATUS'
    });
    
    if (response.success && response.data) {
      popupState.isAuthenticated = response.data.isAuthenticated;
      popupState.user = response.data.user;
    }
  } catch (error) {
    console.error('Error checking auth status:', error);
    popupState.isAuthenticated = false;
  }
}

async function checkRecordingStatus(): Promise<void> {
  try {
    const response = await sendMessageToBackground({
      type: 'RECORDING_STATUS'
    });
    
    if (response.success && response.data) {
      popupState.isRecording = response.data.isRecording;
      if (popupState.isRecording && response.data.startTime) {
        recordingStartTime = response.data.startTime;
        startRecordingTimer();
      }
    }
  } catch (error) {
    console.error('Error checking recording status:', error);
  }
}

async function loadUsageStats(): Promise<void> {
  try {
    // This would typically fetch from the API
    // For now, we'll use mock data
    popupState.usageStats = {
      meetingsThisMonth: 1,
      minutesUsed: 45,
      meetingsLimit: 3,
      minutesLimit: 300
    };
  } catch (error) {
    console.error('Error loading usage stats:', error);
  }
}

function updateUI(): void {
  hideAllSections();
  
  if (!popupState.isAuthenticated) {
    showAuthSection();
  } else {
    showMainContent();
    updateMeetingInfo();
    updateRecordingControls();
    updateUsageStats();
  }
  
  updateUserStatus();
}

function updateUserStatus(): void {
  if (popupState.isAuthenticated) {
    elements.statusIndicator.className = 'status-indicator online';
    elements.statusText.textContent = popupState.user?.name || 'Signed In';
  } else {
    elements.statusIndicator.className = 'status-indicator offline';
    elements.statusText.textContent = 'Not Signed In';
  }
}

function updateMeetingInfo(): void {
  if (!popupState.currentTab) return;
  
  const platform = detectMeetingPlatform(popupState.currentTab.url);
  const platformInfo = getPlatformInfo(platform);
  
  elements.platformIcon.textContent = platformInfo.icon;
  elements.platformText.textContent = platformInfo.name;
  elements.meetingTitle.textContent = popupState.currentTab.title || 'Current Meeting';
}

function updateRecordingControls(): void {
  if (popupState.isRecording) {
    elements.statusIdle.style.display = 'none';
    elements.statusRecording.style.display = 'flex';
    elements.recordBtn.style.display = 'none';
    elements.stopBtn.style.display = 'flex';
  } else {
    elements.statusIdle.style.display = 'flex';
    elements.statusRecording.style.display = 'none';
    elements.recordBtn.style.display = 'flex';
    elements.stopBtn.style.display = 'none';
  }
}

function updateUsageStats(): void {
  if (!popupState.usageStats) return;
  
  const stats = popupState.usageStats;
  
  elements.meetingsCount.textContent = stats.meetingsThisMonth.toString();
  elements.minutesUsed.textContent = stats.minutesUsed.toString();
  
  const usagePercentage = (stats.meetingsThisMonth / stats.meetingsLimit) * 100;
  elements.usageProgress.style.width = `${Math.min(usagePercentage, 100)}%`;
  
  const tier = popupState.user?.subscription_tier || 'free';
  elements.usageText.textContent = tier === 'free' 
    ? `Free plan: ${stats.meetingsThisMonth}/${stats.meetingsLimit} meetings`
    : `Pro plan: ${stats.minutesUsed}/${stats.minutesLimit} minutes`;
}

function setupEventListeners(): void {
  // Auth buttons
  elements.signInBtn.addEventListener('click', handleSignIn);
  elements.signUpBtn.addEventListener('click', handleSignUp);
  
  // Recording controls
  elements.recordBtn.addEventListener('click', handleStartRecording);
  elements.stopBtn.addEventListener('click', handleStopRecording);
  
  // Quick actions
  elements.dashboardBtn.addEventListener('click', () => openWebApp('/dashboard'));
  elements.tasksBtn.addEventListener('click', () => openWebApp('/tasks'));
  elements.settingsBtn.addEventListener('click', () => openWebApp('/settings'));
  
  // Error retry
  elements.retryBtn.addEventListener('click', initializePopup);
}

async function handleStartRecording(): Promise<void> {
  try {
    if (!isMeetingPlatform()) {
      showError('Please navigate to a meeting platform (Google Meet, Zoom, or Teams) to start recording.');
      return;
    }
    
    const response = await sendMessageToBackground({
      type: 'START_RECORDING',
      payload: {
        title: popupState.currentTab?.title
      }
    });
    
    if (response.success) {
      popupState.isRecording = true;
      recordingStartTime = Date.now();
      startRecordingTimer();
      updateRecordingControls();
    } else {
      showError(response.error || 'Failed to start recording');
    }
  } catch (error) {
    console.error('Error starting recording:', error);
    showError('Failed to start recording');
  }
}

async function handleStopRecording(): Promise<void> {
  try {
    const response = await sendMessageToBackground({
      type: 'STOP_RECORDING'
    });
    
    if (response.success) {
      popupState.isRecording = false;
      stopRecordingTimer();
      updateRecordingControls();
      
      // Show success message
      showSuccess('Recording stopped and uploaded successfully!');
    } else {
      showError(response.error || 'Failed to stop recording');
    }
  } catch (error) {
    console.error('Error stopping recording:', error);
    showError('Failed to stop recording');
  }
}

function handleSignIn(): void {
  openWebApp('/auth/signin');
}

function handleSignUp(): void {
  openWebApp('/auth/signup');
}

function openWebApp(path: string = ''): void {
  const baseUrl = 'http://localhost:3000'; // Force localhost for development

  chrome.tabs.create({ url: `${baseUrl}${path}` });
}

function startRecordingTimer(): void {
  recordingTimer = window.setInterval(() => {
    const elapsed = Date.now() - recordingStartTime;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    
    elements.recordingTime.textContent = 
      `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, 1000);
}

function stopRecordingTimer(): void {
  if (recordingTimer) {
    clearInterval(recordingTimer);
    recordingTimer = null;
  }
}

// Utility functions
function detectMeetingPlatform(url: string): string {
  if (!url) return 'other';
  if (url.includes('meet.google.com')) return 'google-meet';
  if (url.includes('zoom.us') || url.includes('zoom.com')) return 'zoom';
  if (url.includes('teams.microsoft.com')) return 'teams';
  return 'other';
}

function getPlatformInfo(platform: string) {
  const platforms = {
    'google-meet': { name: 'Google Meet', icon: '📹' },
    'zoom': { name: 'Zoom', icon: '💻' },
    'teams': { name: 'Microsoft Teams', icon: '👥' },
    'other': { name: 'Meeting Detected', icon: '🎥' }
  };
  
  return platforms[platform as keyof typeof platforms] || platforms.other;
}

function isMeetingPlatform(): boolean {
  if (!popupState.currentTab?.url) return false;
  return detectMeetingPlatform(popupState.currentTab.url) !== 'other';
}

async function sendMessageToBackground(message: any): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage(message, (response) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(response);
      }
    });
  });
}

// UI state management
function hideAllSections(): void {
  elements.loadingSection.style.display = 'none';
  elements.authSection.style.display = 'none';
  elements.mainContent.style.display = 'none';
  elements.errorSection.style.display = 'none';
}

function showLoading(): void {
  hideAllSections();
  elements.loadingSection.style.display = 'block';
}

function showAuthSection(): void {
  hideAllSections();
  elements.authSection.style.display = 'block';
}

function showMainContent(): void {
  hideAllSections();
  elements.mainContent.style.display = 'block';
}

function showError(message: string): void {
  elements.errorMessage.textContent = message;
  elements.errorSection.style.display = 'block';
  
  // Hide error after 5 seconds
  setTimeout(() => {
    elements.errorSection.style.display = 'none';
  }, 5000);
}

function showSuccess(message: string): void {
  // Create temporary success notification
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    background: #059669;
    color: white;
    padding: 12px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 1000;
  `;
  notification.textContent = message;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.remove();
  }, 3000);
}
