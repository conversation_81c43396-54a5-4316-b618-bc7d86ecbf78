'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { apiHelpers } from '@/lib/api';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { 
  MicrophoneIcon,
  PlusIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { formatDate, formatDuration, getPlatformColor } from '@/lib/utils';

export default function MeetingsPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  const { data: meetings, isLoading } = useQuery(
    'meetings',
    () => apiHelpers.getMeetings(50),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="min-w-0 flex-1">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Meetings
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              View your recorded meetings, transcriptions, and extracted action items.
            </p>
          </div>
          <div className="mt-4 flex md:ml-4 md:mt-0">
            <Link
              href="/meetings/upload"
              className="btn btn-primary"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Upload Meeting
            </Link>
          </div>
        </div>

        {/* Meetings List */}
        <div className="bg-white shadow rounded-lg">
          {isLoading ? (
            <div className="px-6 py-8">
              <div className="animate-pulse space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : meetings?.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {meetings.map((meeting: any) => (
                <div key={meeting.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    {/* Meeting Icon */}
                    <div className="flex-shrink-0">
                      <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                        <MicrophoneIcon className="h-6 w-6 text-primary-600" />
                      </div>
                    </div>

                    {/* Meeting Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Link
                            href={`/meetings/${meeting.id}`}
                            className="text-sm font-medium text-gray-900 hover:text-primary-600"
                          >
                            {meeting.title}
                          </Link>
                          <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                            <span className="flex items-center">
                              <ClockIcon className="h-4 w-4 mr-1" />
                              {formatDate(meeting.created_at)}
                            </span>
                            <span>{formatDuration(meeting.duration_minutes)}</span>
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPlatformColor(meeting.platform)}`}>
                              {meeting.platform.replace('-', ' ')}
                            </span>
                          </div>
                        </div>

                        {/* Status */}
                        <div className="flex items-center space-x-2 ml-4">
                          {getStatusIcon(meeting.transcription_status)}
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(meeting.transcription_status)}`}>
                            {meeting.transcription_status}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex-shrink-0">
                      <Link
                        href={`/meetings/${meeting.id}`}
                        className="text-primary-600 hover:text-primary-500 text-sm font-medium"
                      >
                        View Details →
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="px-6 py-12 text-center">
              <MicrophoneIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No meetings yet</h3>
              <p className="mt-1 text-sm text-gray-500">
                Upload your first meeting recording to get started with KaiNote.
              </p>
              <div className="mt-6">
                <Link href="/meetings/upload" className="btn btn-primary">
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Upload Meeting
                </Link>
              </div>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        {meetings?.length > 0 && (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <MicrophoneIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Meetings
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {meetings.length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ClockIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Total Duration
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {formatDuration(meetings.reduce((total: number, meeting: any) => total + meeting.duration_minutes, 0))}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DocumentTextIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Processed
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {meetings.filter((m: any) => m.transcription_status === 'completed').length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
